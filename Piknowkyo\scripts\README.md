# Scripts de Gestion Firestore pour PiKnowKyo

Ce dossier contient les scripts pour gérer les scripts de sessions dans Firestore.

## 📁 Organisation dans Firestore

### Collections

1. **`scripts`** : Contient tous les scripts de sessions
   - Document ID : `{language}_{scriptId}` (ex: `fr_accepter_et_rayonner`)
   - Organisés par catégories automatiques basées sur les tags

2. **`manifests`** : Contient les manifestes par langue
   - Document ID : `{language}` (ex: `fr`, `en`, `es`)
   - Liste toutes les sessions disponibles pour une langue

### Catégories automatiques

Les scripts sont automatiquement catégorisés selon leurs tags :

- **développement-personnel** : estime-de-soi, confiance-en-soi, motivation, croissance-personnelle
- **santé-bien-être** : relaxation, sommeil, guérison, post-opératoire, forme-physique
- **émotionnel** : deuil, acceptation, libération, paix-intérieure, guérison-émotionnelle
- **addictions-blocages** : addictions, blocages-mentaux, blocages-énergétiques, croyances-limitantes, phobies
- **alimentation** : alimentation-saine, gestion-du-poids, boulimie, anorexie
- **abondance-finances** : abondance, richesse, finances, prospérité, manifestation
- **spiritualité** : chakras, énergie, parapsychologie, merkaba, intuition
- **enfants** : enfants, hypnose-enfants, tdah, peurs
- **mémoire-concentration** : mémoire, mémoire-photographique, concentration, tda, clarté

## 🚀 Scripts disponibles

### 1. Upload initial de tous les scripts

```bash
node scripts/uploadScriptsToFirestore.js
```

**Utilisation** : Upload initial de tous les scripts depuis `public/assets/sessionScripts/` vers Firestore.

**Résultat** :
- ✅ 140+ scripts uploadés
- ✅ 3 manifestes créés (fr, en, es)
- ✅ Organisation automatique par catégories

### 2. Mettre à jour un script spécifique

```bash
node scripts/updateScript.js <language> <scriptId>
```

**Exemples** :
```bash
# Mettre à jour un script français
node scripts/updateScript.js fr accepter_et_rayonner

# Mettre à jour un script anglais
node scripts/updateScript.js en inner_journey_to_unshakeable_confidence

# Mettre à jour un script espagnol
node scripts/updateScript.js es aceptar_y_brillar_liberacion_y_fuerza_interior
```

**Utilisation** : Quand vous modifiez un script local, utilisez cette commande pour le synchroniser avec Firestore.

**Fonctionnalités** :
- ✅ Incrémente automatiquement la version
- ✅ Conserve les statistiques existantes (playCount, rating)
- ✅ Met à jour la date de modification
- ✅ Les utilisateurs reçoivent automatiquement la mise à jour

### 3. Vérifier l'état de Firestore

```bash
node scripts/verifyFirestoreUpload.js
```

**Utilisation** : Vérifier que tous les scripts sont bien uploadés et accessibles.

**Affiche** :
- 📊 Nombre de scripts par langue
- 📋 État des manifestes
- ✅ Exemples de scripts uploadés

## 🔧 Workflow de mise à jour

### Quand vous modifiez un script :

1. **Modifiez le fichier local** dans `public/assets/sessionScripts/{language}/{scriptId}.json`

2. **Synchronisez avec Firestore** :
   ```bash
   node scripts/updateScript.js {language} {scriptId}
   ```

3. **Les utilisateurs reçoivent automatiquement la mise à jour** lors de leur prochaine synchronisation

### Exemple complet :

```bash
# 1. Vous modifiez le script local
# public/assets/sessionScripts/fr/accepter_et_rayonner.json

# 2. Vous synchronisez avec Firestore
node scripts/updateScript.js fr accepter_et_rayonner

# 3. Résultat dans la console :
# 🔄 Mise à jour du script: fr/accepter_et_rayonner
# 📊 Version actuelle: 1 → Nouvelle version: 2
# ✅ Script mis à jour avec succès: fr/accepter_et_rayonner (v2)
# 🎉 Mise à jour terminée avec succès !
# 💡 Les utilisateurs recevront automatiquement la mise à jour lors de leur prochaine synchronisation.
```

## 📊 Structure des documents Firestore

### Document Script
```json
{
  "id": "accepter_et_rayonner",
  "title": "Accepter et Rayonner : Libération et Force Intérieure",
  "description": "Ce script d'hypnose profonde...",
  "benefits": "Acceptation des choses immuables...",
  "type": "hypnosis",
  "language": "fr",
  "durationMinutes": 45,
  "category": "développement-personnel",
  "tags": ["acceptation", "estime-de-soi", "confiance-en-soi"],
  "isPremium": false,
  "price": 0.0,
  "createdAt": "2025-05-01",
  "updatedAt": "2025-01-15T10:30:00.000Z",
  "version": 2,
  "script": [...],
  "playCount": 0,
  "rating": 0,
  "ratingCount": 0
}
```

### Document Manifeste
```json
{
  "language": "fr",
  "sessions": [...],
  "totalSessions": 48,
  "lastUpdated": "2025-01-15T10:30:00.000Z",
  "version": 1
}
```

## ⚠️ Prérequis

1. **Index Firestore** : Créez l'index composite nécessaire via ce lien :
   [Créer l'index Firestore](https://console.firebase.google.com/v1/r/project/piknowkyo-777/firestore/indexes?create_composite=Ck1wcm9qZWN0cy9waWtub3dreW8tNzc3L2RhdGFiYXNlcy8oZGVmYXVsdCkvY29sbGVjdGlvbkdyb3Vwcy9zY3JpcHRzL2luZGV4ZXMvXxABGgwKCGxhbmd1YWdlEAEaCQoFdGl0bGUQARoMCghfX25hbWVfXxAB)

2. **Firebase installé** : `npm install firebase` (déjà fait)

3. **Configuration Firebase** : Les scripts utilisent la configuration du projet PiKnowKyo

## 🎯 Avantages

- ✅ **Synchronisation automatique** : Les utilisateurs reçoivent les mises à jour automatiquement
- ✅ **Versioning** : Chaque modification incrémente la version
- ✅ **Organisation logique** : Catégorisation automatique par thèmes
- ✅ **Statistiques conservées** : playCount, rating, etc. sont préservés
- ✅ **Facilité d'utilisation** : Une seule commande pour mettre à jour
- ✅ **Sécurité** : Pas de perte de données, versioning complet
