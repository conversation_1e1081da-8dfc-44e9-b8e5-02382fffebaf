// Script pour initialiser la configuration des prix dans Firebase
import { initializeApp } from 'firebase/app';
import { getFirestore, doc, setDoc, serverTimestamp } from 'firebase/firestore';

// Configuration Firebase (même que dans l'app)
const firebaseConfig = {
  apiKey: "AIzaSyABy8bGDxVU-sM2nqfsp3jDm8JPtg_v4kM",
  authDomain: "piknowkyo-777.firebaseapp.com",
  projectId: "piknowkyo-777",
  storageBucket: "piknowkyo-777.firebasestorage.app",
  messagingSenderId: "************",
  appId: "1:************:web:9ece9c5c2ce600a8c206c7",
  measurementId: "G-DSXRMZ4JP2"
};

// Initialiser Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

// Configuration des prix par défaut
const defaultPricingConfig = {
  premiumPrice: 4.99,
  premiumCurrency: '€',
  trialDays: 14,
  features: {
    free: [
      'Méditations guidées de base',
      'Histoires relaxantes',
      'Musique de fond',
      'Voix de base',
      'Accès limité aux jeux'
    ],
    premium: [
      'Toutes les méditations',
      'Toutes les histoires',
      'Sons binauraux avancés',
      'Voix premium',
      'Accès illimité aux jeux',
      'Personnalisation avancée',
      'Support prioritaire',
      'Synchronisation multi-appareils'
    ]
  },
  createdAt: serverTimestamp(),
  updatedAt: serverTimestamp()
};

async function initPricingConfig() {
  try {
    console.log('🚀 Initialisation de la configuration des prix...');
    
    const docRef = doc(db, 'config', 'pricing');
    await setDoc(docRef, defaultPricingConfig);
    
    console.log('✅ Configuration des prix initialisée avec succès !');
    console.log(`💰 Prix premium: ${defaultPricingConfig.premiumPrice}${defaultPricingConfig.premiumCurrency}/mois`);
    console.log(`🎁 Période d'essai: ${defaultPricingConfig.trialDays} jours`);
    console.log('📋 Fonctionnalités configurées pour les plans gratuit et premium');
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Erreur lors de l\'initialisation:', error);
    process.exit(1);
  }
}

// Exécuter le script
initPricingConfig();
