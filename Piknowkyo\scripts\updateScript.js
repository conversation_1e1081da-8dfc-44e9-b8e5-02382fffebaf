// Script pour mettre à jour un script spécifique dans Firestore
// Usage: node scripts/updateScript.js <language> <scriptId>
// Exemple: node scripts/updateScript.js fr accepter_et_rayonner

import { initializeApp } from 'firebase/app';
import { getFirestore, doc, setDoc, getDoc } from 'firebase/firestore';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Pour obtenir __dirname dans un module ES
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration Firebase
const firebaseConfig = {
  apiKey: "AIzaSyABy8bGDxVU-sM2nqfsp3jDm8JPtg_v4kM",
  authDomain: "piknowkyo-777.firebaseapp.com",
  projectId: "piknowkyo-777",
  storageBucket: "piknowkyo-777.firebasestorage.app",
  messagingSenderId: "375619599814",
  appId: "1:375619599814:web:9ece9c5c2ce600a8c206c7",
  measurementId: "G-DSXRMZ4JP2"
};

// Initialiser Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

// Catégories pour organiser les scripts
const TAG_CATEGORIES = {
  'développement-personnel': ['estime-de-soi', 'confiance-en-soi', 'motivation', 'croissance-personnelle'],
  'santé-bien-être': ['relaxation', 'sommeil', 'guérison', 'post-opératoire', 'forme-physique'],
  'émotionnel': ['deuil', 'acceptation', 'libération', 'paix-intérieure', 'guérison-émotionnelle'],
  'addictions-blocages': ['addictions', 'blocages-mentaux', 'blocages-énergétiques', 'croyances-limitantes', 'phobies'],
  'alimentation': ['alimentation-saine', 'gestion-du-poids', 'boulimie', 'anorexie'],
  'abondance-finances': ['abondance', 'richesse', 'finances', 'prospérité', 'manifestation'],
  'spiritualité': ['chakras', 'énergie', 'parapsychologie', 'merkaba', 'intuition'],
  'enfants': ['enfants', 'hypnose-enfants', 'tdah', 'peurs'],
  'mémoire-concentration': ['mémoire', 'mémoire-photographique', 'concentration', 'tda', 'clarté']
};

// Fonction pour déterminer la catégorie d'un script basé sur ses tags
function determineCategory(tags) {
  for (const [category, categoryTags] of Object.entries(TAG_CATEGORIES)) {
    if (tags.some(tag => categoryTags.includes(tag))) {
      return category;
    }
  }
  return 'autres';
}

// Fonction pour lire un fichier JSON
function readJsonFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(content);
  } catch (error) {
    console.error(`Erreur lors de la lecture de ${filePath}:`, error.message);
    return null;
  }
}

// Fonction pour mettre à jour un script
async function updateScript(language, scriptId) {
  try {
    console.log(`🔄 Mise à jour du script: ${language}/${scriptId}`);

    // Lire le script local
    const scriptPath = path.join(__dirname, '..', 'public', 'assets', 'sessionScripts', language, `${scriptId}.json`);
    const scriptData = readJsonFile(scriptPath);
    
    if (!scriptData) {
      console.error(`❌ Script non trouvé: ${scriptPath}`);
      return false;
    }

    // Récupérer la version actuelle dans Firestore
    const docId = `${language}_${scriptId}`;
    const docRef = doc(db, 'scripts', docId);
    const currentDoc = await getDoc(docRef);
    
    let currentVersion = 1;
    if (currentDoc.exists()) {
      currentVersion = (currentDoc.data().version || 1) + 1;
      console.log(`📊 Version actuelle: ${currentVersion - 1} → Nouvelle version: ${currentVersion}`);
    } else {
      console.log(`📊 Nouveau script, version: ${currentVersion}`);
    }

    const category = determineCategory(scriptData.tags || []);
    
    // Structure du document dans Firestore
    const firestoreDoc = {
      // Métadonnées de base
      id: scriptData.id,
      title: scriptData.title,
      description: scriptData.description || '',
      benefits: scriptData.benefits || '',
      
      // Informations de session
      type: scriptData.type,
      language: language,
      durationMinutes: scriptData.durationMinutes || 0,
      estimatedDuration: scriptData.estimatedDuration || scriptData.durationMinutes || 0,
      
      // Organisation
      category: category,
      tags: scriptData.tags || [],
      
      // Monétisation
      isPremium: scriptData.isPremium || false,
      price: scriptData.price || 0.0,
      
      // Métadonnées temporelles
      createdAt: scriptData.createdAt || new Date().toISOString().split('T')[0],
      updatedAt: new Date().toISOString(),
      version: currentVersion,
      isNew: scriptData.isNew || false,
      
      // Contenu du script
      script: scriptData.script || [],
      
      // Métadonnées additionnelles
      imageUrl: scriptData.imageUrl || null,
      audioUrl: scriptData.audioUrl || null,
      
      // Conserver les statistiques existantes ou initialiser
      playCount: currentDoc.exists() ? (currentDoc.data().playCount || 0) : 0,
      rating: currentDoc.exists() ? (currentDoc.data().rating || 0) : 0,
      ratingCount: currentDoc.exists() ? (currentDoc.data().ratingCount || 0) : 0
    };

    await setDoc(docRef, firestoreDoc);
    console.log(`✅ Script mis à jour avec succès: ${language}/${scriptId} (v${currentVersion})`);
    
    return true;
  } catch (error) {
    console.error(`❌ Erreur lors de la mise à jour de ${language}/${scriptId}:`, error.message);
    return false;
  }
}

// Fonction principale
async function main() {
  const args = process.argv.slice(2);
  
  if (args.length !== 2) {
    console.log('Usage: node scripts/updateScript.js <language> <scriptId>');
    console.log('Exemple: node scripts/updateScript.js fr accepter_et_rayonner');
    process.exit(1);
  }

  const [language, scriptId] = args;
  
  if (!['fr', 'en', 'es'].includes(language)) {
    console.error('❌ Langue non supportée. Utilisez: fr, en, ou es');
    process.exit(1);
  }

  console.log(`🚀 Mise à jour du script ${scriptId} en ${language}...\n`);
  
  const success = await updateScript(language, scriptId);
  
  if (success) {
    console.log('\n🎉 Mise à jour terminée avec succès !');
    console.log('💡 Les utilisateurs recevront automatiquement la mise à jour lors de leur prochaine synchronisation.');
  } else {
    console.log('\n❌ Échec de la mise à jour.');
    process.exit(1);
  }
}

// Exécuter le script
main().catch(console.error);
