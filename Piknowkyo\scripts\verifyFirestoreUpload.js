// Script pour vérifier que les scripts sont bien uploadés dans Firestore
// Usage: node scripts/verifyFirestoreUpload.js

import { initializeApp } from 'firebase/app';
import { getFirestore, collection, getDocs, query, where, orderBy } from 'firebase/firestore';

// Configuration Firebase
const firebaseConfig = {
  apiKey: "AIzaSyABy8bGDxVU-sM2nqfsp3jDm8JPtg_v4kM",
  authDomain: "piknowkyo-777.firebaseapp.com",
  projectId: "piknowkyo-777",
  storageBucket: "piknowkyo-777.firebasestorage.app",
  messagingSenderId: "375619599814",
  appId: "1:375619599814:web:9ece9c5c2ce600a8c206c7",
  measurementId: "G-DSXRMZ4JP2"
};

// Initialiser Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

async function verifyUpload() {
  console.log('🔍 Vérification des scripts dans Firestore...\n');

  try {
    // Vérifier les scripts par langue
    const languages = ['fr', 'en', 'es'];
    
    for (const lang of languages) {
      console.log(`📁 Langue: ${lang}`);
      
      // Requête pour récupérer tous les scripts d'une langue
      const scriptsQuery = query(
        collection(db, 'scripts'),
        where('language', '==', lang),
        orderBy('title')
      );
      
      const querySnapshot = await getDocs(scriptsQuery);
      console.log(`   📊 Nombre de scripts: ${querySnapshot.size}`);
      
      // Afficher quelques exemples
      let count = 0;
      querySnapshot.forEach((doc) => {
        if (count < 3) {
          const data = doc.data();
          console.log(`   ✅ ${data.title} (${data.type}, ${data.durationMinutes}min)`);
          count++;
        }
      });
      
      if (querySnapshot.size > 3) {
        console.log(`   ... et ${querySnapshot.size - 3} autres scripts`);
      }
      console.log('');
    }

    // Vérifier les manifestes
    console.log('📋 Vérification des manifestes...');
    const manifestsSnapshot = await getDocs(collection(db, 'manifests'));
    console.log(`   📊 Nombre de manifestes: ${manifestsSnapshot.size}`);
    
    manifestsSnapshot.forEach((doc) => {
      const data = doc.data();
      console.log(`   ✅ Manifeste ${data.language}: ${data.totalSessions} sessions`);
    });

    console.log('\n🎉 Vérification terminée avec succès !');
    
  } catch (error) {
    console.error('❌ Erreur lors de la vérification:', error.message);
    
    if (error.message.includes('index')) {
      console.log('\n💡 Il semble que l\'index Firestore ne soit pas encore créé.');
      console.log('   Allez sur ce lien pour créer l\'index nécessaire:');
      console.log('   https://console.firebase.google.com/v1/r/project/piknowkyo-777/firestore/indexes?create_composite=Ck1wcm9qZWN0cy9waWtub3dreW8tNzc3L2RhdGFiYXNlcy8oZGVmYXVsdCkvY29sbGVjdGlvbkdyb3Vwcy9zY3JpcHRzL2luZGV4ZXMvXxABGgwKCGxhbmd1YWdlEAEaCQoFdGl0bGUQARoMCghfX25hbWVfXxAB');
    }
  }
}

// Exécuter la vérification
verifyUpload().catch(console.error);
