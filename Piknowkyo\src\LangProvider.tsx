import { createContext, useContext, useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useAuth } from './contexts/AuthContext';
import { UserPreferencesService } from './services/userPreferencesService';

export type Language = 'fr' | 'en' | 'es';

const defaultLang: Language = 'fr';

interface LangContextType {
  lang: Language;
  setLang: (l: Language) => void;
}

export const LangContext = createContext<LangContextType>({
  lang: defaultLang,
  setLang: () => {},
});

export const useLang = () => useContext(LangContext);

export const LangProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { i18n } = useTranslation();
  const { user, isAuthenticated } = useAuth();
  const [lang, setLang] = useState<Language>(defaultLang);
  const [isLoading, setIsLoading] = useState(true);

  // Charger la langue depuis Firebase quand l'utilisateur se connecte
  useEffect(() => {
    const loadUserLanguage = async () => {
      if (isAuthenticated && user) {
        try {
          const preferences = await UserPreferencesService.getUserPreferences(user.uid);
          if (preferences.language !== lang) {
            setLang(preferences.language);
            i18n.changeLanguage(preferences.language);
          }
        } catch (error) {
          console.error('Erreur lors du chargement de la langue utilisateur:', error);
        }
      }
      setIsLoading(false);
    };

    loadUserLanguage();
  }, [isAuthenticated, user, i18n]);

  // Sync with i18n language changes
  useEffect(() => {
    if (!isLoading) {
      const currentLang = i18n.language as Language;
      if (currentLang !== lang) {
        setLang(currentLang);
      }
    }
  }, [i18n.language, lang, isLoading]);

  // Update i18n and save to Firebase when lang changes
  const handleSetLang = async (newLang: Language) => {
    setLang(newLang);
    i18n.changeLanguage(newLang);

    // Sauvegarder dans Firebase si l'utilisateur est connecté
    if (isAuthenticated && user) {
      try {
        await UserPreferencesService.updateSpecificPreference(user.uid, 'language', newLang);
      } catch (error) {
        console.error('Erreur lors de la sauvegarde de la langue:', error);
      }
    }
  };

  return (
    <LangContext.Provider value={{ lang, setLang: handleSetLang }}>
      {children}
    </LangContext.Provider>
  );
};
