import React from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { FiGlobe } from 'react-icons/fi';
import { useUserPreferences } from '../hooks/useUserPreferences';
import { Language } from '../LangProvider';

const LanguageContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const LanguageSelect = styled.select`
  background: ${({ theme }) => theme.surface};
  color: ${({ theme }) => theme.text};
  border: 1px solid ${({ theme }) => theme.border};
  border-radius: 6px;
  padding: 0.5rem;
  font-size: 0.9rem;
  cursor: pointer;

  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.primary};
  }

  option {
    background: ${({ theme }) => theme.surface};
    color: ${({ theme }) => theme.text};
  }
`;

const LanguageIcon = styled(FiGlobe)`
  color: ${({ theme }) => theme.textSecondary};
  font-size: 1.1rem;
`;

const LanguageSwitcher: React.FC = () => {
  const { i18n, t } = useTranslation();
  const { updateLanguage, preferences, isAuthenticated } = useUserPreferences();

  const handleLanguageChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    const newLanguage = event.target.value as Language;
    
    if (isAuthenticated) {
      // Utiliser le système de préférences pour les utilisateurs connectés
      updateLanguage(newLanguage);
    } else {
      // Fallback pour les utilisateurs non connectés
      i18n.changeLanguage(newLanguage);
    }
  };

  const currentLanguage = isAuthenticated ? preferences.language : i18n.language;

  return (
    <LanguageContainer>
      <LanguageIcon />
      <LanguageSelect value={currentLanguage} onChange={handleLanguageChange}>
        <option value="fr">{t('languages.french')}</option>
        <option value="en">{t('languages.english')}</option>
        <option value="es">{t('languages.spanish')}</option>
      </LanguageSelect>
    </LanguageContainer>
  );
};

export default LanguageSwitcher;
