import React from 'react';
import { useAuth } from '../contexts/AuthContext';
import AuthModal from './AuthModal';
import SplashScreen from './SplashScreen';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requireAuth?: boolean; // Par défaut true, peut être false pour les routes publiques
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ 
  children, 
  requireAuth = true 
}) => {
  const { isAuthenticated, loading } = useAuth();

  // Afficher le splash screen pendant le chargement de l'authentification
  if (loading) {
    return <SplashScreen />;
  }

  // Si l'authentification n'est pas requise, afficher le contenu
  if (!requireAuth) {
    return <>{children}</>;
  }

  // Si l'utilisateur n'est pas authentifié, afficher le modal d'authentification
  // et bloquer l'accès au contenu
  if (!isAuthenticated) {
    return (
      <div style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        zIndex: 9999
      }}>
        <AuthModal
          isOpen={true}
          onClose={() => {}} // Pas de fermeture possible
          initialMode="signup"
        />
      </div>
    );
  }

  // Utilisateur authentifié, afficher le contenu
  return <>{children}</>;
};

export default ProtectedRoute;
