import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import { useTranslation } from 'react-i18next';
import { useSessions, useAppDispatch, useSync, useOfflineStatus } from '../store/hooks';
import { fetchSessions, createSession, setCurrentSession } from '../store/slices/sessionsSlice';
import { performFullSync } from '../store/slices/syncSlice';
import { Session } from '../models';

const Container = styled.div`
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 10px;
`;

const Title = styled.h2`
  margin: 0;
  color: #333;
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
`;

const Button = styled.button<{ $variant?: 'primary' | 'secondary' | 'success' }>`
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  font-size: 0.9rem;
  transition: all 0.2s ease;

  ${props => {
    switch (props.$variant) {
      case 'primary':
        return `
          background: #007bff;
          color: white;
          &:hover { background: #0056b3; }
        `;
      case 'success':
        return `
          background: #28a745;
          color: white;
          &:hover { background: #1e7e34; }
        `;
      default:
        return `
          background: #6c757d;
          color: white;
          &:hover { background: #545b62; }
        `;
    }
  }}

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
`;

const StatusBar = styled.div`
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 20px;
  font-size: 0.9rem;
`;

const StatusItem = styled.div`
  margin-bottom: 4px;

  &:last-child {
    margin-bottom: 0;
  }
`;

const SessionsList = styled.div`
  display: grid;
  gap: 12px;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
`;

const SessionCard = styled.div<{ $isSelected?: boolean }>`
  background: white;
  border: 2px solid ${props => props.$isSelected ? '#007bff' : '#dee2e6'};
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    border-color: #007bff;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
`;

const SessionTitle = styled.h3`
  margin: 0 0 8px 0;
  color: #333;
  font-size: 1.1rem;
`;

const SessionDescription = styled.p`
  margin: 0 0 12px 0;
  color: #666;
  font-size: 0.9rem;
  line-height: 1.4;
`;

const SessionMeta = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.8rem;
  color: #888;
`;

const LoadingSpinner = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
  font-size: 1rem;
  color: #666;
`;

const ErrorMessage = styled.div`
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  color: #721c24;
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 20px;
`;

const OfflineIndicator = styled.div`
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  color: #856404;
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 20px;
  text-align: center;
`;

const ReduxExample: React.FC = () => {
  const { t } = useTranslation();
  const { lang } = useLang();
  const dispatch = useAppDispatch();
  const { sessions, currentSession, isLoading, error } = useSessions();
  const { isSyncing, syncStatus, syncErrors } = useSync();
  const { isOffline, hasPendingChanges } = useOfflineStatus();
  const [sessionCounter, setSessionCounter] = useState(1);

  useEffect(() => {
    // Charger les sessions au montage du composant
    if (lang) {
      dispatch(fetchSessionsByLanguage(lang as Language));
    }
  }, [dispatch, lang]);

  const handleCreateSession = async () => {
    const newSession: Omit<Session, 'id'> = {
      title: `Session Redux ${sessionCounter}`,
      description: `Session créée via Redux le ${new Date().toLocaleString()}`,
      type: 'meditation',
      category: 'relaxation',
      language: 'fr',
      estimatedDuration: Math.floor(Math.random() * 30) + 5, // 5-35 minutes
      tags: ['redux', 'test', 'demo'],
      benefits: ['Démonstration Redux', 'Test de persistance'],
    };

    try {
      await dispatch(createSession(newSession)).unwrap();
      setSessionCounter(prev => prev + 1);
    } catch (error) {
      console.error('Erreur lors de la création de la session:', error);
    }
  };

  const handleSessionClick = (session: Session) => {
    dispatch(setCurrentSession(session));
  };

  const handleSync = () => {
    dispatch(performFullSync());
  };

  const handleRefresh = () => {
    dispatch(fetchSessions());
  };

  if (isLoading && sessions.length === 0) {
    return (
      <Container>
        <LoadingSpinner>{t('reduxExample.loading')}</LoadingSpinner>
      </Container>
    );
  }

  return (
    <Container>
      <Header>
        <Title>{t('reduxExample.title')}</Title>
        <ButtonGroup>
          <Button
            $variant="primary"
            onClick={handleCreateSession}
            disabled={isLoading}
          >
            {isLoading ? 'Création...' : 'Créer Session'}
          </Button>
          <Button onClick={handleRefresh} disabled={isLoading}>
            Actualiser
          </Button>
          <Button
            $variant="success"
            onClick={handleSync}
            disabled={!navigator.onLine || isSyncing}
          >
            {isSyncing ? 'Sync...' : 'Synchroniser'}
          </Button>
        </ButtonGroup>
      </Header>

      {isOffline && (
        <OfflineIndicator>
          🔌 Mode hors ligne - Les modifications seront synchronisées lors de la reconnexion
          {hasPendingChanges && ' (Changements en attente)'}
        </OfflineIndicator>
      )}

      <StatusBar>
        <StatusItem><strong>{t('reduxExample.reduxState')}</strong></StatusItem>
        <StatusItem>• Sessions chargées : {sessions.length}</StatusItem>
        <StatusItem>• Session sélectionnée : {currentSession?.title || 'Aucune'}</StatusItem>
        <StatusItem>• Statut réseau : {isOffline ? '🔴 Hors ligne' : '🟢 En ligne'}</StatusItem>
        <StatusItem>• Synchronisation : {isSyncing ? '🔄 En cours' : syncStatus === 'success' ? '✅ OK' : syncStatus === 'error' ? '❌ Erreur' : '⏸️ Idle'}</StatusItem>
        <StatusItem>• Changements en attente : {hasPendingChanges ? '⚠️ Oui' : '✅ Non'}</StatusItem>
      </StatusBar>

      {error && (
        <ErrorMessage>
          ❌ Erreur: {error}
        </ErrorMessage>
      )}

      {syncErrors.length > 0 && (
        <ErrorMessage>
          ❌ Erreurs de synchronisation: {syncErrors.join(', ')}
        </ErrorMessage>
      )}

      <SessionsList>
        {sessions.map((session) => (
          <SessionCard
            key={session.id}
            $isSelected={currentSession?.id === session.id}
            onClick={() => handleSessionClick(session)}
          >
            <SessionTitle>{session.title}</SessionTitle>
            <SessionDescription>{session.description}</SessionDescription>
            <SessionMeta>
              <span>{session.type} • {session.category}</span>
              <span>{session.estimatedDuration || session.durationMinutes || 'N/A'} min</span>
            </SessionMeta>
          </SessionCard>
        ))}
      </SessionsList>

      {sessions.length === 0 && !isLoading && (
        <LoadingSpinner>
          Aucune session disponible. Créez votre première session !
        </LoadingSpinner>
      )}
    </Container>
  );
};

export default ReduxExample;
