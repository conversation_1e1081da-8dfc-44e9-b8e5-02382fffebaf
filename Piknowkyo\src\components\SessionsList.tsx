// src/components/SessionsList.tsx

import React, { useState, useMemo, useEffect, useContext } from 'react';
import { SessionManifestEntry } from '../models';
import styled, { css, DefaultTheme } from 'styled-components';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { FiSearch, FiGrid, FiList, FiX, FiFilter, FiChevronDown, FiLoader, FiLock, FiStar, FiPlay, FiZap, FiWifi, FiWifiOff, FiRefreshCw } from 'react-icons/fi';
import { useLang, Language } from '../LangProvider';
import { useTranslation } from 'react-i18next';
import { useAppSelector } from '../store/hooks';
import { MonetizationService } from '../services/monetizationService';
import MonetizationModal from './MonetizationModal';
import { useAuth } from '../contexts/AuthContext';
import { useFilteredSessions } from '../hooks/useAccessControl';
import LockedSessionCard from './LockedSessionCard';
import SubscriptionStatus from './SubscriptionStatus';
import { useOfflineSync, useCurrentLanguageSessions } from '../hooks/useOfflineSync';

// --- Styled Components ---

const PageContainer = styled.div`
  padding: 1rem;
  max-width: 1200px;
  margin: 0 auto;
  color: ${({ theme }) => theme.text};
`;

const HeaderControls = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1.5rem;

  @media (min-width: 768px) {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
  }
`;

const SyncStatusBar = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: ${({ theme }) => theme.surfaceAlt};
  border-radius: 20px;
  font-size: 0.85rem;
  color: ${({ theme }) => theme.textSecondary};
  margin-bottom: 1rem;
`;

const SyncStatusIcon = styled.div<{ $status: 'online' | 'offline' | 'syncing' | 'error' }>`
  display: flex;
  align-items: center;
  color: ${({ $status, theme }) => {
    switch ($status) {
      case 'online': return '#10B981';
      case 'offline': return '#EF4444';
      case 'syncing': return theme.primary;
      case 'error': return '#F59E0B';
      default: return theme.textSecondary;
    }
  }};

  ${({ $status }) => $status === 'syncing' && css`
    animation: spin 1s linear infinite;
  `}

  @keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }
`;

const RefreshButton = styled.button`
  background: transparent;
  border: none;
  color: ${({ theme }) => theme.primary};
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  display: flex;
  align-items: center;
  font-size: 1rem;

  &:hover {
    background: ${({ theme }) => theme.surfaceAlt};
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const SearchInputContainer = styled.div`
  display: flex;
  align-items: center;
  background: ${({ theme }) => theme.surfaceAlt};
  border-radius: 25px;
  padding: 0.3rem 0.5rem 0.3rem 1rem;
  flex-grow: 1;
  box-shadow: ${({ theme }) => theme.cardShadow && theme.cardShadow.startsWith('0') ? `inset ${theme.cardShadow}` : 'inset 0 1px 3px rgba(0,0,0,0.06)'};

  svg {
    color: ${({ theme }) => theme.textSecondary};
    margin-right: 0.75rem;
    font-size: 1.2rem;
  }

  input {
    flex-grow: 1;
    border: none;
    background: transparent;
    padding: 0.6rem 0.2rem;
    font-size: 1rem;
    color: ${({ theme }) => theme.text};
    outline: none;

    &::placeholder {
      color: ${({ theme }) => theme.textMuted};
    }
  }
`;

const ClearButton = styled.button`
  background: transparent;
  border: none;
  color: ${({ theme }) => theme.textSecondary};
  cursor: pointer;
  padding: 0.5rem;
  font-size: 1.2rem;
  display: flex;
  align-items: center;

  &:hover {
    color: ${({ theme }) => theme.primary};
  }
`;

const FilterAndDisplayControls = styled.div`
  display: flex;
  gap: 0.75rem;
  align-items: center;

  @media (max-width: 767px) {
    justify-content: space-between;
  }
`;

const FilterDropdownContainer = styled.div`
  position: relative;
`;

const FilterButton = styled.button`
  background: ${({ theme }) => theme.surface};
  color: ${({ theme }) => theme.textSecondary};
  border: 1px solid ${({ theme }) => theme.border};
  border-radius: 20px;
  padding: 0.6rem 1rem;
  font-size: 0.9rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: ${({ theme }) => theme.cardShadow};

  &:hover {
    border-color: ${({ theme }) => theme.primary};
    color: ${({ theme }) => theme.primary};
  }
`;

const FilterPopup = styled.div`
  position: absolute;
  top: calc(100% + 0.5rem);
  left: 0;
  background: ${({ theme }) => theme.surface};
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  padding: 1rem;
  z-index: 10;
  width: 250px;

  h4 {
    margin-top: 0;
    margin-bottom: 0.75rem;
    font-size: 1rem;
    color: ${({ theme }) => theme.primary};
  }

  label {
    display: block;
    margin-bottom: 0.3rem;
    font-size: 0.9rem;
    color: ${({ theme }) => theme.textSecondary};
  }

  select {
    width: 100%;
    padding: 0.6rem;
    border-radius: 6px;
    border: 1px solid ${({ theme }) => theme.border};
    background: ${({ theme }) => theme.inputBackground || theme.surfaceAlt};
    color: ${({ theme }) => theme.text};
    font-size: 0.9rem;
    margin-bottom: 1rem;

    &:last-child {
      margin-bottom: 0;
    }
  }
`;

interface DisplayToggleButtonProps {
  $isActive?: boolean;
}

const DisplayToggleButton = styled.button<DisplayToggleButtonProps>`
  background: ${({ theme, $isActive }) => $isActive ? theme.primary : theme.surface};
  color: ${({ theme, $isActive }) => $isActive ? theme.textLight : theme.textSecondary};
  border: 1px solid ${({ theme, $isActive }) => $isActive ? theme.primary : theme.border};
  border-radius: 50%;
  padding: 0.6rem;
  font-size: 1.2rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: ${({ theme }) => theme.cardShadow};

  &:hover {
    border-color: ${({ theme }) => theme.primary};
    color: ${({ theme, $isActive }) => !$isActive && theme.primary};
  }
`;

// CORRECTION: Utiliser $viewMode dans l'interface et la définition
interface SessionListContainerProps {
  $viewMode: 'grid' | 'list';
}

const SessionListContainer = styled.div<SessionListContainerProps>`
  display: grid;
  gap: 1.5rem;

  ${({ $viewMode }) => $viewMode === 'grid' && css`
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    @media (max-width: 600px) {
      grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    }
  `}

  ${({ $viewMode }) => $viewMode === 'list' && css`
    grid-template-columns: 1fr;
  `}
`;

interface SessionCardProps {
  $viewMode: 'grid' | 'list';
}

const SessionCard = styled(Link)<SessionCardProps>`
  background: ${({ theme }) => theme.surface};
  border-radius: 12px;
  box-shadow: ${({ theme }) => theme.cardShadow};
  color: inherit;
  text-decoration: none;
  overflow: hidden;
  transition: transform 0.2s ease-out, box-shadow 0.2s ease-out;
  display: flex;
  flex-direction: column;
  position: relative;

  &:hover {
    transform: translateY(-4px);
    box-shadow: ${({ theme }) => theme.cardHoverShadow || '0 6px 18px rgba(0,0,0,0.1)'};
  }

  ${({ $viewMode }) => $viewMode === 'list' && css`
    flex-direction: row;
    align-items: center;
    padding: 1rem;
    gap: 1rem;
  `}
`;

interface CardImagePlaceholderProps {
  $viewMode: 'grid' | 'list';
  imageUrl?: string | null;
  'data-type-emoji'?: string;
}

const CardImagePlaceholder = styled.div<CardImagePlaceholderProps>`
  background-color: ${({ theme }) => theme.surfaceAlt};
  display: flex;
  align-items: center;
  justify-content: center;
  color: ${({ theme }) => theme.textMuted};
  font-size: 2rem;
  background-image: ${({ imageUrl }) => imageUrl ? `url(${imageUrl})` : 'none'};
  background-size: cover;
  background-position: center;

  ${({ $viewMode }) => $viewMode === 'grid' && css`
    height: 150px;
    border-bottom: 1px solid ${({ theme }) => theme.border};
  `}

  ${({ $viewMode }) => $viewMode === 'list' && css`
    width: 100px;
    height: 100px;
    min-width: 100px;
    border-radius: 8px;
    flex-shrink: 0;
  `}

  &::before {
    content: ${(props) => !props.imageUrl && props['data-type-emoji'] ? `"${props['data-type-emoji']}"` : '""'};
  }

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
  }
`;

interface CardContentProps {
  $viewMode: 'grid' | 'list';
}

const CardContent = styled.div<CardContentProps>`
  padding: 1.2rem;
  flex-grow: 1;
  display: flex;
  flex-direction: column;

  ${({ $viewMode }) => $viewMode === 'list' && css`
    padding: 0;
  `}
`;

const CardTitle = styled.h3`
  margin: 0 0 0.5rem 0;
  color: ${({ theme }) => theme.primary};
  font-size: 1.2rem;
  line-height: 1.3;
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const PremiumBadge = styled.span`
  background: ${({ theme }) => theme.primary};
  color: white;
  font-size: 0.7rem;
  padding: 0.2rem 0.5rem;
  border-radius: 12px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.25rem;
`;

const RestrictedOverlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.9));
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  color: white;
  font-size: 0.9rem;
  text-align: center;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.85), rgba(0, 0, 0, 0.95));
    transform: scale(1.02);
  }

  .lock-icon {
    font-size: 2.5rem;
    margin-bottom: 0.75rem;
    opacity: 0.9;
    animation: pulse 2s infinite;
  }

  .unlock-text {
    font-weight: 600;
    margin-bottom: 1rem;
    font-size: 1rem;
  }

  .cta-buttons {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    width: 100%;
    max-width: 200px;
  }

  @keyframes pulse {
    0%, 100% { opacity: 0.9; transform: scale(1); }
    50% { opacity: 1; transform: scale(1.1); }
  }
`;

const CTAButton = styled.button<{ $variant?: 'primary' | 'secondary' }>`
  padding: 0.75rem 1rem;
  border: none;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;

  ${({ $variant, theme }) => $variant === 'primary' ? `
    background: ${theme.primary};
    color: white;

    &:hover {
      background: ${theme.primaryDark || theme.primary};
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    }
  ` : `
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);

    &:hover {
      background: rgba(255, 255, 255, 0.3);
      transform: translateY(-2px);
    }
  `}
`;

const SessionCardWrapper = styled.div<{ $canAccess: boolean }>`
  position: relative;
  cursor: ${({ $canAccess }) => $canAccess ? 'pointer' : 'default'};
  transition: all 0.3s ease;

  &:hover {
    transform: ${({ $canAccess }) => $canAccess ? 'translateY(-4px)' : 'none'};
  }
`;

const AccessibleSessionCard = styled(Link)<{ $viewMode: 'grid' | 'list' }>`
  background: ${({ theme }) => theme.surface};
  border-radius: 12px;
  box-shadow: ${({ theme }) => theme.cardShadow};
  color: inherit;
  text-decoration: none;
  overflow: hidden;
  transition: transform 0.2s ease-out, box-shadow 0.2s ease-out;
  display: flex;
  flex-direction: column;
  position: relative;

  &:hover {
    transform: translateY(-4px);
    box-shadow: ${({ theme }) => theme.cardHoverShadow || '0 6px 18px rgba(0,0,0,0.1)'};
  }

  ${({ $viewMode }) => $viewMode === 'list' && css`
    flex-direction: row;
    align-items: center;
    padding: 1rem;
    gap: 1rem;
  `}

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1));
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &:hover::after {
    opacity: 1;
  }
`;

const PlayButton = styled.div`
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: ${({ theme }) => theme.primary};
  color: white;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  opacity: 0;
  transition: all 0.3s ease;
  box-shadow: 0 4px 20px rgba(0,0,0,0.3);

  ${AccessibleSessionCard}:hover & {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.1);
  }
`;

const CardMeta = styled.div`
  font-size: 0.8rem;
  color: ${({ theme }) => theme.textMuted};
  margin-bottom: 0.5rem;

  span {
    margin-right: 0.8rem;
    &:last-child {
      margin-right: 0;
    }
  }
`;

const CardTags = styled.div`
  font-size: 0.8rem;
  color: ${({ theme }) => theme.accent};
  margin-top: 0.5rem;

  span {
    background-color: ${({ theme }) => `${theme.accent}20`};
    padding: 0.2rem 0.5rem;
    border-radius: 10px;
    margin-right: 0.4rem;
    margin-bottom: 0.4rem;
    display: inline-block;
  }
`;

const NoResultsMessage = styled.div`
  text-align: center;
  padding: 2rem;
  color: ${({ theme }) => theme.textSecondary};
  font-style: italic;
`;

const LoadingContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 3rem;
  font-size: 1.2rem;
  color: ${({ theme }) => theme.primary};

  svg {
    margin-right: 0.5rem;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

const ErrorMessage = styled.p`
  color: ${({ theme }) => theme.errorColor || 'red'};
  text-align: center;
  padding: 2rem;
`;

const getTypeEmoji = (type: string) => {
  switch (type.toLowerCase()) {
    case 'hypnosis': return '🌀';
    case 'silence': return '🙊';
    case 'affirmation': return '🪁';
    case 'meditation': return '🧘';
    case 'training': return '🏋️';
    case 'story': return '📖';
    case 'custom': return '✨';
    default: return '💡';
  }
};

const SessionsList: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { lang } = useLang();
  const { t } = useTranslation();

  // Nouveau système d'authentification
  const { isAuthenticated, userProfile } = useAuth();

  // Redux state pour la monétisation (pour compatibilité)
  const { currentPlan, temporaryUnlocks } = useAppSelector(state => state.monetization);

  // Nouveau système offline-first
  const {
    isOffline,
    syncStatus,
    isLoading,
    forceSync,
    getSyncStatus
  } = useOfflineSync();

  const {
    sessions: currentLanguageSessions,
    syncStatus: currentSyncStatus,
    isFresh
  } = useCurrentLanguageSessions();

  const queryParams = new URLSearchParams(location.search);
  const initialType = queryParams.get('type') || '';

  // Convertir les sessions en SessionManifestEntry pour compatibilité
  const allSessionMetadata = useMemo(() => {
    return currentLanguageSessions.map(session => ({
      id: session.id,
      title: session.title,
      type: session.type,
      estimatedDuration: session.estimatedDuration || 0,
      tags: session.tags || [],
      imageUrl: session.imageUrl,
      isPremium: session.isPremium || false
    }));
  }, [currentLanguageSessions]);

  // États pour la monétisation
  const [showMonetizationModal, setShowMonetizationModal] = useState(false);
  const [selectedSession, setSelectedSession] = useState<SessionManifestEntry | null>(null);

  const [search, setSearch] = useState('');
  const [type, setType] = useState(initialType);
  const [duration, setDuration] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showFilters, setShowFilters] = useState(false);

  useEffect(() => {
    setType(initialType);
  }, [initialType]);

  // Fonction pour obtenir le message de statut de synchronisation
  const getSyncStatusMessage = () => {
    if (isOffline) {
      return t('sync.offline', 'Mode hors ligne - Données locales');
    }

    switch (currentSyncStatus) {
      case 'fresh':
        return t('sync.fresh', 'Données à jour');
      case 'recent':
        return t('sync.recent', 'Données récentes');
      case 'stale':
        return t('sync.stale', 'Données anciennes - Synchronisation recommandée');
      case 'never-synced':
        return t('sync.neverSynced', 'Jamais synchronisé');
      case 'no-data':
        return t('sync.noData', 'Aucune donnée');
      default:
        return t('sync.unknown', 'Statut inconnu');
    }
  };

  const getSyncIcon = () => {
    if (syncStatus === 'syncing') return <FiRefreshCw />;
    if (isOffline) return <FiWifiOff />;
    return <FiWifi />;
  };

  const getSyncIconStatus = (): 'online' | 'offline' | 'syncing' | 'error' => {
    if (syncStatus === 'syncing') return 'syncing';
    if (isOffline) return 'offline';
    if (currentSyncStatus === 'stale' || currentSyncStatus === 'never-synced') return 'error';
    return 'online';
  };

  // Filtrer les sessions selon les critères de recherche
  const searchFilteredSessions = useMemo(() => {
    return allSessionMetadata.filter((s) => {
      const searchLower = search.toLowerCase();
      const matchesSearch =
        s.title.toLowerCase().includes(searchLower) ||
        (s.tags || []).some((tagItem) => tagItem.toLowerCase().includes(searchLower));

      const matchesType = type ? s.type === type : true;

      const sessionDuration = s.estimatedDuration || 0;
      const matchesDuration = duration ?
        (duration === "0-15" && sessionDuration <= 15) ||
        (duration === "15-30" && sessionDuration > 15 && sessionDuration <= 30) ||
        (duration === "30+" && sessionDuration > 30)
        : true;

      return matchesSearch && matchesType && matchesDuration;
    });
  }, [allSessionMetadata, search, type, duration]);

  // Convertir les SessionManifestEntry en Session pour le hook useFilteredSessions
  const sessionsForFiltering = useMemo(() => {
    return searchFilteredSessions.map(s => ({
      ...s,
      description: '', // Les manifestes n'ont pas de description
      language: lang as any,
      script: [],
      audio: null,
      benefits: [],
      comments: []
    }));
  }, [searchFilteredSessions, lang]);

  // Utiliser notre hook pour filtrer selon l'accès utilisateur
  const { accessibleSessions, lockedSessions } = useFilteredSessions(sessionsForFiltering);

  const uniqueSessionTypes = useMemo(() => {
    if (!allSessionMetadata.length) return [];
    const types = new Set(allSessionMetadata.map(s => s.type));
    return Array.from(types).map(tVal => ({ value: tVal, label: t(`sessionTypes.${tVal}`, tVal.charAt(0).toUpperCase() + tVal.slice(1)) }));
  }, [allSessionMetadata, t]);

  // Fonctions de gestion des clics
  const handleSessionClick = (session: SessionManifestEntry, canAccess: boolean) => {
    if (canAccess) {
      navigate(`/sessions/${session.id}`);
    } else {
      setSelectedSession(session);
      setShowMonetizationModal(true);
    }
  };

  const handleWatchAdClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (selectedSession) {
      setShowMonetizationModal(true);
    }
  };

  const handleUpgradeClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    navigate('/monetization');
  };

  return (
    <PageContainer>
      {/* Statut d'abonnement */}
      {isAuthenticated && userProfile && (
        <SubscriptionStatus showUpgradeButton={true} compact={false} />
      )}

      {/* Barre de statut de synchronisation */}
      <SyncStatusBar>
        <SyncStatusIcon $status={getSyncIconStatus()}>
          {getSyncIcon()}
        </SyncStatusIcon>
        <span>{getSyncStatusMessage()}</span>
        <span>({allSessionMetadata.length} scripts)</span>
        {!isOffline && (
          <RefreshButton
            onClick={() => lang && forceSync(lang as Language)}
            disabled={syncStatus === 'syncing'}
            title={t('sync.refresh', 'Actualiser')}
          >
            <FiRefreshCw />
          </RefreshButton>
        )}
      </SyncStatusBar>

      {isLoading && (
        <LoadingContainer>
          <FiLoader /> {t('loading.sessions', 'Chargement des sessions...')}
        </LoadingContainer>
      )}

      <HeaderControls>
        <SearchInputContainer>
          <FiSearch />
          <input
            type="text"
            placeholder={t('sessions.searchPlaceholder', "Rechercher une séance...")}
            value={search}
            onChange={(e) => setSearch(e.target.value)}
          />
          {search && <ClearButton onClick={() => setSearch('')}><FiX /></ClearButton>}
        </SearchInputContainer>
        <FilterAndDisplayControls>
          <FilterDropdownContainer>
            <FilterButton onClick={() => setShowFilters(!showFilters)}>
              <FiFilter /> {t('sessions.filters', 'Filtres')} <FiChevronDown style={{ transform: showFilters ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'transform 0.2s'}}/>
            </FilterButton>
            {showFilters && (
              <FilterPopup>
                <h4>{t('sessions.filterBy', 'Filtrer par')}</h4>
                <label htmlFor="sessionType">{t('sessions.sessionType', 'Type de séance')}</label>
                <select id="sessionType" value={type} onChange={(e) => setType(e.target.value)}>
                  <option value="">{t('sessions.allTypes', 'Tous types')}</option>
                  {uniqueSessionTypes.map(tOption => <option key={tOption.value} value={tOption.value}>{getTypeEmoji(tOption.value)} {tOption.label}</option>)}
                </select>

                <label htmlFor="sessionDuration">{t('sessions.duration.label', 'Durée')}</label>
                <select id="sessionDuration" value={duration} onChange={(e) => setDuration(e.target.value)}>
                  <option value="">{t('sessions.allDurations', 'Toutes durées')}</option>
                  <option value="0-15">{t('sessions.duration.under15', 'Moins de 15 min')}</option>
                  <option value="15-30">{t('sessions.duration.15to30', '15 - 30 min')}</option>
                  <option value="30+">{t('sessions.duration.over30', 'Plus de 30 min')}</option>
                </select>
              </FilterPopup>
            )}
          </FilterDropdownContainer>
          <DisplayToggleButton $isActive={viewMode === 'grid'} onClick={() => setViewMode('grid')} title={t('sessions.gridView', "Vue Grille")}>
            <FiGrid />
          </DisplayToggleButton>
          <DisplayToggleButton $isActive={viewMode === 'list'} onClick={() => setViewMode('list')} title={t('sessions.listView', "Vue Liste")}>
            <FiList />
          </DisplayToggleButton>
        </FilterAndDisplayControls>
      </HeaderControls>

      {(accessibleSessions.length > 0 || lockedSessions.length > 0) ? (
        <SessionListContainer $viewMode={viewMode}>
          {/* Sessions accessibles */}
          {accessibleSessions.map((session) => {
            // Retrouver les métadonnées originales
            const s = searchFilteredSessions.find(meta => meta.id === session.id);
            if (!s) return null;

            return (
              <AccessibleSessionCard key={s.id} to={`/sessions/${s.id}`} $viewMode={viewMode}>
                <CardImagePlaceholder
                  $viewMode={viewMode}
                  imageUrl={s.imageUrl}
                  data-type-emoji={!s.imageUrl ? getTypeEmoji(s.type) : undefined}
                >
                  <PlayButton>
                    <FiPlay />
                  </PlayButton>
                </CardImagePlaceholder>
                <CardContent $viewMode={viewMode}>
                  <CardTitle>
                    {s.title}
                    {session.isPremium && (
                      <PremiumBadge>
                        <FiStar size={10} />
                        {t('sessions.premium', 'Premium')}
                      </PremiumBadge>
                    )}
                  </CardTitle>
                  <CardMeta>
                    <span>{t('sessions.durationLabel', 'Durée')}: {s.estimatedDuration || 'N/A'} {t('units.min', 'min')}</span>
                  </CardMeta>
                  {(s.tags && s.tags.length > 0) && (
                    <CardTags>
                      {(s.tags || []).slice(0, viewMode === 'list' ? 2 : 3).map((tag: string) => <span key={tag}>#{tag}</span>)}
                    </CardTags>
                  )}
                </CardContent>
              </AccessibleSessionCard>
            );
          })}

          {/* Sessions verrouillées */}
          {lockedSessions.map((session) => {
            // Retrouver les métadonnées originales
            const s = searchFilteredSessions.find(meta => meta.id === session.id);
            if (!s) return null;

            return (
              <LockedSessionCard
                key={s.id}
                session={session}
                onUnlock={(sessionId) => {
                  // Gérer le déverrouillage temporaire
                  console.log('Session déverrouillée temporairement:', sessionId);
                  // Ici on pourrait ajouter la logique de déverrouillage temporaire
                }}
              />
            );
          })}
        </SessionListContainer>
      ) : (
        <NoResultsMessage>
            {search || type || duration
                ? t('sessions.noResultsMatchCriteria', "Aucune séance ne correspond à vos critères.")
                : t('sessions.noSessionsAvailable', "Aucune session disponible pour le moment.")
            }
        </NoResultsMessage>
      )}

      {/* Modal de monétisation */}
      <MonetizationModal
        isOpen={showMonetizationModal}
        onClose={() => {
          setShowMonetizationModal(false);
          setSelectedSession(null);
        }}
        type="session"
        sessionId={selectedSession?.id}
        sessionTitle={selectedSession?.title}
        onUpgrade={() => {
          navigate('/monetization');
        }}
      />
    </PageContainer>
  );
};

export default SessionsList;