import { useEffect, useCallback } from 'react';
import { useAppDispatch, useAppSelector } from '../store/hooks';
import { 
  fetchSessionsByLanguage, 
  syncSessionsInBackground, 
  forceRefreshSessions,
  setOfflineStatus,
  setSyncStatus 
} from '../store/slices/sessionsSlice';
import { useLang } from '../LangProvider';
import { Language } from '../LangProvider';

export const useOfflineSync = () => {
  const dispatch = useAppDispatch();
  const { lang } = useLang();
  const { 
    sessionsByLanguage, 
    lastSyncByLanguage, 
    isOffline, 
    syncStatus,
    isLoading 
  } = useAppSelector(state => state.sessions);

  // Surveiller l'état de la connexion
  useEffect(() => {
    const handleOnline = () => {
      console.log('📶 Connexion rétablie');
      dispatch(setOfflineStatus(false));
      
      // Synchroniser automatiquement quand on revient en ligne
      if (lang) {
        dispatch(syncSessionsInBackground(lang as Language));
      }
    };

    const handleOffline = () => {
      console.log('📵 Connexion perdue - Mode hors ligne');
      dispatch(setOfflineStatus(true));
    };

    // État initial
    dispatch(setOfflineStatus(!navigator.onLine));

    // Écouter les changements de connexion
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [dispatch, lang]);

  // Charger les sessions pour la langue courante
  const loadSessions = useCallback((language: Language = lang as Language) => {
    if (!language) return;
    
    console.log(`🔄 Chargement des sessions pour ${language}`);
    dispatch(fetchSessionsByLanguage(language));
  }, [dispatch, lang]);

  // Synchronisation en arrière-plan périodique
  useEffect(() => {
    if (!lang || isOffline || !lastSyncByLanguage || typeof lastSyncByLanguage !== 'object') return;

    const syncInterval = setInterval(() => {
      const lastSync = lastSyncByLanguage[lang as Language];
      const timeSinceLastSync = lastSync ? Date.now() - lastSync : Infinity;

      // Synchroniser toutes les 10 minutes si en ligne
      if (timeSinceLastSync > 10 * 60 * 1000) {
        console.log(`🔄 Synchronisation automatique pour ${lang}`);
        dispatch(syncSessionsInBackground(lang as Language));
      }
    }, 60 * 1000); // Vérifier chaque minute

    return () => clearInterval(syncInterval);
  }, [dispatch, lang, isOffline, lastSyncByLanguage]);

  // Forcer la synchronisation
  const forceSync = useCallback((language: Language = lang as Language) => {
    if (!language) return;
    
    console.log(`🔄 Synchronisation forcée pour ${language}`);
    dispatch(forceRefreshSessions(language));
  }, [dispatch, lang]);

  // Obtenir les sessions pour une langue spécifique
  const getSessionsForLanguage = useCallback((language: Language) => {
    if (!sessionsByLanguage || typeof sessionsByLanguage !== 'object') {
      console.warn('sessionsByLanguage is not available yet');
      return [];
    }
    return sessionsByLanguage[language] || [];
  }, [sessionsByLanguage]);

  // Vérifier si les données sont fraîches
  const isDataFresh = useCallback((language: Language, maxAgeMinutes: number = 5) => {
    if (!lastSyncByLanguage || typeof lastSyncByLanguage !== 'object') {
      console.warn('lastSyncByLanguage is not available yet');
      return false;
    }
    const lastSync = lastSyncByLanguage[language];
    if (!lastSync) return false;

    const ageMinutes = (Date.now() - lastSync) / (1000 * 60);
    return ageMinutes < maxAgeMinutes;
  }, [lastSyncByLanguage]);

  // Obtenir le statut de synchronisation pour une langue
  const getSyncStatus = useCallback((language: Language) => {
    if (!sessionsByLanguage || typeof sessionsByLanguage !== 'object' ||
        !lastSyncByLanguage || typeof lastSyncByLanguage !== 'object') {
      console.warn('sessionsByLanguage or lastSyncByLanguage is not available yet');
      return 'no-data';
    }

    const sessions = sessionsByLanguage[language];
    const lastSync = lastSyncByLanguage[language];

    if (!sessions || sessions.length === 0) {
      return 'no-data';
    }

    if (!lastSync) {
      return 'never-synced';
    }

    const ageMinutes = (Date.now() - lastSync) / (1000 * 60);

    if (ageMinutes < 5) {
      return 'fresh';
    } else if (ageMinutes < 60) {
      return 'recent';
    } else {
      return 'stale';
    }
  }, [sessionsByLanguage, lastSyncByLanguage]);

  // Charger automatiquement les sessions au démarrage
  useEffect(() => {
    if (lang && sessionsByLanguage && typeof sessionsByLanguage === 'object') {
      const languageSessions = sessionsByLanguage[lang as Language];
      if (!languageSessions || languageSessions.length === 0) {
        loadSessions(lang as Language);
      }
    }
  }, [lang, sessionsByLanguage, loadSessions]);

  return {
    // État
    isOffline,
    syncStatus,
    isLoading,
    sessionsByLanguage,
    lastSyncByLanguage,
    
    // Actions
    loadSessions,
    forceSync,
    
    // Helpers
    getSessionsForLanguage,
    isDataFresh,
    getSyncStatus,
  };
};

// Hook pour obtenir les sessions de la langue courante
export const useCurrentLanguageSessions = () => {
  const { lang } = useLang();
  const { getSessionsForLanguage, getSyncStatus, isDataFresh, sessionsByLanguage } = useOfflineSync();

  // Vérifier si le store est prêt
  const isStoreReady = sessionsByLanguage && typeof sessionsByLanguage === 'object';

  const sessions = lang && isStoreReady ? getSessionsForLanguage(lang as Language) : [];
  const syncStatus = lang && isStoreReady ? getSyncStatus(lang as Language) : 'no-data';
  const isFresh = lang && isStoreReady ? isDataFresh(lang as Language) : false;

  return {
    sessions,
    syncStatus,
    isFresh,
    count: sessions.length,
    isStoreReady,
  };
};

// Hook pour surveiller les mises à jour en temps réel
export const useRealtimeSync = () => {
  const { lang } = useLang();
  const dispatch = useAppDispatch();
  
  useEffect(() => {
    if (!lang) return;
    
    // Ici on pourrait ajouter un listener Firestore en temps réel
    // pour détecter les changements et mettre à jour automatiquement
    
    // Pour l'instant, on fait une synchronisation périodique
    const realtimeInterval = setInterval(() => {
      if (navigator.onLine) {
        dispatch(syncSessionsInBackground(lang as Language));
      }
    }, 5 * 60 * 1000); // Toutes les 5 minutes
    
    return () => clearInterval(realtimeInterval);
  }, [lang, dispatch]);
};
