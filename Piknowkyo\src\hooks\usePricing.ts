import { useEffect } from 'react';
import { useAppDispatch, useAppSelector } from '../store/hooks';
import { setPremiumPrice } from '../store/slices/monetizationSlice';
import { PricingService } from '../services/pricingService';

export const usePricing = () => {
  const dispatch = useAppDispatch();
  const { premiumPrice, premiumCurrency } = useAppSelector(state => state.monetization);

  useEffect(() => {
    const loadPricing = async () => {
      try {
        const pricing = await PricingService.getPremiumPrice();
        
        // Mettre à jour Redux seulement si les prix ont changé
        if (pricing.price !== premiumPrice || pricing.currency !== premiumCurrency) {
          dispatch(setPremiumPrice({
            price: pricing.price,
            currency: pricing.currency
          }));
        }
      } catch (error) {
        console.error('Erreur lors du chargement des prix:', error);
      }
    };

    loadPricing();
  }, [dispatch, premiumPrice, premiumCurrency]);

  return {
    premiumPrice,
    premiumCurrency,
    refreshPricing: async () => {
      const pricing = await PricingService.getPremiumPrice();
      dispatch(setPremiumPrice({
        price: pricing.price,
        currency: pricing.currency
      }));
    }
  };
};
