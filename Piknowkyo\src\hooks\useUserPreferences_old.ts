import { useEffect, useCallback } from 'react';
import { useAppDispatch, useAppSelector } from '../store/hooks';
import { useAuth } from '../contexts/AuthContext';
import { useLang, Language } from '../LangProvider';
import { useTheme } from '../ThemeProvider';
import { useTranslation } from 'react-i18next';
import {
  loadUserPreferences,
  saveUserPreferences,
  updateLanguagePreference,
  updateThemePreference,
  updateLocalPreference,
  updateLocalNestedPreference,
  resetUserPreferences,
  syncWithAppState,
  markAsSaved,
} from '../store/slices/userPreferencesSlice';
import { UserPreferences } from '../services/userPreferencesService';

export const useUserPreferences = () => {
  const dispatch = useAppDispatch();
  const { user } = useAuth();
  const { lang, setLang } = useLang();
  const { darkMode, toggleTheme } = useTheme();
  const { i18n } = useTranslation();
  
  const {
    preferences,
    isLoading,
    error,
    hasUnsavedChanges,
    lastSyncedAt,
  } = useAppSelector((state) => state.userPreferences);

  // Charger les préférences utilisateur au montage
  useEffect(() => {
    if (user?.uid) {
      dispatch(loadUserPreferences(user.uid));
    }
  }, [user?.uid, dispatch]);

  // Synchroniser les préférences avec l'état global de l'app (seulement au chargement initial)
  useEffect(() => {
    if (user?.uid && preferences && lastSyncedAt) {
      // Synchroniser la langue seulement si elle est différente
      if (preferences.language !== lang) {
        setLang(preferences.language);
        i18n.changeLanguage(preferences.language);
      }

      // Synchroniser le thème seulement s'il est différent
      const isDarkMode = preferences.theme === 'dark';
      if (isDarkMode !== darkMode) {
        toggleTheme();
      }
    }
  }, [preferences.language, preferences.theme, lastSyncedAt]); // Dépendances spécifiques pour éviter la boucle

  // Sauvegarder automatiquement les changements non sauvegardés
  useEffect(() => {
    if (user?.uid && hasUnsavedChanges) {
      const timeoutId = setTimeout(() => {
        dispatch(saveUserPreferences({ userId: user.uid, preferences }));
      }, 2000); // Sauvegarder après 2 secondes d'inactivité

      return () => clearTimeout(timeoutId);
    }
  }, [user?.uid, hasUnsavedChanges, preferences, dispatch]);

  // Fonctions pour mettre à jour les préférences
  const updatePreference = useCallback((key: keyof UserPreferences, value: any) => {
    dispatch(updateLocalPreference({ key, value }));
  }, [dispatch]);

  const updateNestedPreference = useCallback((section: keyof UserPreferences, key: string, value: any) => {
    dispatch(updateLocalNestedPreference({ section, key, value }));
  }, [dispatch]);

  const updateLanguage = useCallback(async (language: Language) => {
    if (user?.uid) {
      // Mettre à jour immédiatement l'interface
      setLang(language);
      i18n.changeLanguage(language);
      
      // Sauvegarder dans Firebase
      try {
        await dispatch(updateLanguagePreference({ userId: user.uid, language })).unwrap();
      } catch (error) {
        console.error('Erreur lors de la mise à jour de la langue:', error);
        // Revenir à la langue précédente en cas d'erreur
        setLang(preferences.language);
        i18n.changeLanguage(preferences.language);
      }
    }
  }, [user?.uid, setLang, i18n, dispatch, preferences.language]);

  const updateTheme = useCallback(async (theme: 'light' | 'dark') => {
    if (user?.uid) {
      // Mettre à jour immédiatement l'interface
      const shouldBeDark = theme === 'dark';
      if (shouldBeDark !== darkMode) {
        toggleTheme();
      }
      
      // Sauvegarder dans Firebase
      try {
        await dispatch(updateThemePreference({ userId: user.uid, theme })).unwrap();
      } catch (error) {
        console.error('Erreur lors de la mise à jour du thème:', error);
        // Revenir au thème précédent en cas d'erreur
        if (shouldBeDark === darkMode) {
          toggleTheme();
        }
      }
    }
  }, [user?.uid, darkMode, toggleTheme, dispatch]);

  const savePreferences = useCallback(async () => {
    if (user?.uid) {
      try {
        await dispatch(saveUserPreferences({ userId: user.uid, preferences })).unwrap();
        return true;
      } catch (error) {
        console.error('Erreur lors de la sauvegarde:', error);
        return false;
      }
    }
    return false;
  }, [user?.uid, preferences, dispatch]);

  const resetToDefaults = useCallback(async () => {
    if (user?.uid) {
      try {
        await dispatch(resetUserPreferences(user.uid)).unwrap();
        return true;
      } catch (error) {
        console.error('Erreur lors de la réinitialisation:', error);
        return false;
      }
    }
    return false;
  }, [user?.uid, dispatch]);

  const markPreferencesAsSaved = useCallback(() => {
    dispatch(markAsSaved());
  }, [dispatch]);

  // Calculer le statut de synchronisation
  const getSyncStatus = useCallback(() => {
    if (!user?.uid) return 'not-authenticated';
    if (isLoading) return 'syncing';
    if (error) return 'error';
    if (hasUnsavedChanges) return 'pending';
    if (lastSyncedAt) {
      const timeSinceSync = Date.now() - lastSyncedAt;
      if (timeSinceSync < 60000) return 'fresh'; // Moins d'1 minute
      if (timeSinceSync < 300000) return 'recent'; // Moins de 5 minutes
      return 'stale'; // Plus de 5 minutes
    }
    return 'never-synced';
  }, [user?.uid, isLoading, error, hasUnsavedChanges, lastSyncedAt]);

  return {
    // État
    preferences,
    isLoading,
    error,
    hasUnsavedChanges,
    lastSyncedAt,
    syncStatus: getSyncStatus(),
    
    // Actions
    updatePreference,
    updateNestedPreference,
    updateLanguage,
    updateTheme,
    savePreferences,
    resetToDefaults,
    markPreferencesAsSaved,
    
    // Utilitaires
    isAuthenticated: !!user?.uid,
  };
};
