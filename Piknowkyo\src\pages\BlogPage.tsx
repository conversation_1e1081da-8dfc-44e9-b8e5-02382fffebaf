// src/pages/BlogPage.tsx

import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useLang } from '../LangProvider';
import { useAuth } from '../contexts/AuthContext';
import {
  FiMessageSquare, FiSend, FiUser, FiEdit, FiThumbsUp, FiMessageCircle as FiCommentIcon, FiClock, FiLoader, FiAlertCircle, FiUserX
} from 'react-icons/fi';
import { BlogService, BlogPost as BlogPostType } from '../services/blogService';


// --- Types ---
type BlogPost = BlogPostType;

// --- Styled Components ---

const PageContainer = styled.div`
  padding: 1.5rem;
  max-width: 800px;
  margin: 0 auto;
  color: ${({ theme }) => theme.text};
`;

const PageHeader = styled.div`
  text-align: center;
  margin-bottom: 2.5rem;
  h1 {
    font-size: 2.4rem;
    color: ${({ theme }) => theme.primary};
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
  }
  p {
    font-size: 1rem;
    color: ${({ theme }) => theme.textSecondary};
    line-height: 1.6;
  }
`;

const ControlsBar = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 2rem;
  padding: 1rem;
  background-color: ${({ theme }) => theme.surfaceAlt};
  border-radius: 12px;
`;

const SearchInput = styled.input`
  flex-grow: 1;
  min-width: 200px;
  padding: 0.7rem 1rem;
  border-radius: 8px;
  border: 1px solid ${({ theme }) => theme.border};
  background: ${({ theme }) => theme.inputBackground};
  color: ${({ theme }) => theme.text};
  font-size: 1rem;
  &:focus { /* ... (styles de focus) ... */ }
`;

const CategorySelect = styled.select`
  padding: 0.7rem 1rem;
  border-radius: 8px;
  border: 1px solid ${({ theme }) => theme.border};
  background: ${({ theme }) => theme.inputBackground};
  color: ${({ theme }) => theme.text};
  font-size: 1rem;
  min-width: 180px;
  &:focus { /* ... (styles de focus) ... */ }
`;


const PostFormCard = styled.div`
  background: ${({ theme }) => theme.surface};
  border-radius: 12px;
  box-shadow: ${({ theme }) => theme.cardShadow};
  margin-bottom: 2.5rem;
  padding: 1.5rem;
`;

const SectionTitle = styled.h2`
  font-size: 1.4rem;
  color: ${({ theme }) => theme.primary};
  margin-top: 0;
  margin-bottom: 1.5rem; /* Peut être ajusté par style en ligne si besoin */
  padding-bottom: 0.8rem;
  border-bottom: 1px solid ${({ theme }) => theme.border};
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const TextArea = styled.textarea`
  width: 100%;
  min-height: 100px;
  border-radius: 8px;
  border: 1px solid ${({ theme }) => theme.border};
  padding: 0.8rem 1rem;
  font-size: 1rem;
  margin-bottom: 1rem;
  resize: vertical;
  background: ${({ theme }) => theme.inputBackground};
  color: ${({ theme }) => theme.text};
  &:focus { /* ... (styles de focus) ... */ }
`;

const FormActions = styled.div`
  display: flex;
  justify-content: space-between; /* Pour espacer le select et le bouton */
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
`;

const CategoryInputGroup = styled.div`
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    min-width: 200px;
    label {
        font-size: 0.9rem;
        color: ${({ theme }) => theme.textSecondary};
    }
`;


const Button = styled.button` /* ... (styles de bouton comme dans SettingsPage) ... */ `;

const PostCard = styled.div`
  background: ${({ theme }) => theme.surface};
  border-radius: 12px;
  box-shadow: ${({ theme }) => theme.cardShadow};
  margin-bottom: 1.5rem;
  padding: 1.5rem;
  border-left: 4px solid ${({ theme }) => theme.primary};
`;

const PostHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
  color: ${({ theme }) => theme.textSecondary};
  font-size: 0.85rem;
`;

const AuthorPseudo = styled.span`
  font-weight: 600;
  color: ${({ theme }) => theme.accent};
  display: flex;
  align-items: center;
  gap: 0.3rem;
  svg { opacity: 0.8; }
`;

const PostDate = styled.span`
  display: flex;
  align-items: center;
  gap: 0.3rem;
`;

const PostContent = styled.p`
  font-size: 1rem;
  line-height: 1.7;
  color: ${({ theme }) => theme.text};
  white-space: pre-wrap; /* Respecter les retours à la ligne */
  margin-bottom: 1rem;
`;

const PostFooter = styled.div`
  display: flex;
  align-items: center;
  gap: 1.5rem;
  font-size: 0.9rem;
  color: ${({ theme }) => theme.textMuted};
  padding-top: 1rem;
  border-top: 1px solid ${({ theme }) => theme.border}80;
`;

const ActionButton = styled.button<{ $liked?: boolean }>`
  background: none;
  border: none;
  color: ${({ theme, $liked }) => $liked ? theme.primary : theme.textMuted};
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.4rem;
  font-size: 0.9rem;
  padding: 0.3rem 0.5rem;
  border-radius: 6px;
  transition: color 0.2s, background-color 0.2s;

  &:hover {
    color: ${({ theme }) => theme.primary};
    background-color: ${({ theme }) => theme.primary}1A;
  }
  svg {
    font-size: 1.1rem;
  }
`;

const LoadingContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
  color: ${({ theme }) => theme.primary};
  min-height: 70vh; /* Occupe plus de place */
  svg { font-size: 3.5rem; margin-bottom: 1.5rem; animation: spin 1s linear infinite; }
  @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
`;

const ErrorMessage = styled.div`
  color: ${({ theme }) => theme.errorColor || 'red'};
  text-align: center;
  padding: 2rem;
  background-color: ${({ theme }) => theme.surfaceAlt};
  border-radius: 8px;
  border: 1px solid ${({ theme }) => theme.errorColor || 'red'}33;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  min-height: 50vh;
  justify-content: center;

  p { margin-bottom: 1rem; }
  a {
    color: ${({ theme }) => theme.primary};
    text-decoration: underline;
    font-weight: 500;
  }
`;

const InfoMessage = styled.div`
  text-align: center;
  padding: 2rem;
  color: ${({ theme }) => theme.textSecondary};
  background-color: ${({ theme }) => theme.surfaceAlt};
  border-radius: 12px;
  font-style: italic;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  svg {
    font-size: 2.5rem;
    opacity: 0.7;
  }
`;

const ALL_CATEGORIES = ["général", "gratitude", "défis", "inspirations", "questions"]; // Exemple

// Fonction pour générer un pseudo anonyme simple (à améliorer pour plus d'unicité si besoin)
const generateAnonymousPseudo = (uid: string) => {
    const animals = ["Loup", "Aigle", "Renard", "Ours", "Cerf", "Hibou", "Faucon", "Lion", "Tigre", "Panthère"];
    const adjectives = ["Serein", "Sage", "Curieux", "Fort", "Paisible", "Lumineux", "Agile", "Intrépide", "Calme", "Créatif"];
    // Simple hash pour un peu de consistance, mais pas cryptographiquement sécurisé
    const hash = uid.split("").reduce((acc, char) => acc + char.charCodeAt(0), 0);
    return `${adjectives[hash % adjectives.length]} ${animals[hash % animals.length]}`;
};


const BlogPage: React.FC = () => {
  const { t } = useTranslation();
  const { lang } = useLang();
  const { user } = useAuth();

  const [posts, setPosts] = useState<BlogPost[]>([]);
  const [newPostContent, setNewPostContent] = useState('');
  const [newPostCategory, setNewPostCategory] = useState<string>(ALL_CATEGORIES[0]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');


  // Charger les posts depuis Firebase
  useEffect(() => {
    const loadPosts = async () => {
      setIsLoading(true);
      try {
        const fetchedPosts = await BlogService.getPosts(20, selectedCategory || undefined);

        // Filtrer par terme de recherche côté client
        let filtered = fetchedPosts;
        if (searchTerm) {
          filtered = fetchedPosts.filter(p =>
            p.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
            p.authorPseudo.toLowerCase().includes(searchTerm.toLowerCase()) ||
            (p.tags || []).some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
          );
        }

        setPosts(filtered);
        setError(null);
      } catch (error) {
        console.error("Error fetching blog posts:", error);
        setError(t('errors.cantLoadBlogPosts', "Impossible de charger les messages du blog."));
      } finally {
        setIsLoading(false);
      }
    };

    loadPosts();
  }, [selectedCategory, searchTerm, t]);

  const handlePostSubmit = async () => {
    if (!newPostContent.trim() || !user) return;
    setIsSubmitting(true);

    try {
      // Extraire les hashtags du contenu
      const tags = newPostContent.toLowerCase().match(/#\w+/g)?.map(tag => tag.substring(1)) || [];

      await BlogService.createPost(
        user.uid,
        newPostContent,
        newPostCategory,
        tags
      );

      // Recharger les posts après création
      const fetchedPosts = await BlogService.getPosts(20, selectedCategory || undefined);
      setPosts(fetchedPosts);

      setNewPostContent('');
      setNewPostCategory(ALL_CATEGORIES[0]);
    } catch (error) {
      console.error("Error adding new post:", error);
      alert(t('errors.cantAddPost', "Erreur lors de la publication du message."));
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleLikePost = async (postId: string) => {
    if (!user) return;

    try {
      await BlogService.toggleLike(postId, user.uid);

      // Mettre à jour l'état local immédiatement pour une meilleure UX
      setPosts(prevPosts => prevPosts.map(p => {
        if (p.id === postId) {
          const liked = p.likes.includes(user.uid);
          return {
            ...p,
            likes: liked
              ? p.likes.filter(uid => uid !== user.uid)
              : [...p.likes, user.uid]
          };
        }
        return p;
      }));
    } catch (error) {
      console.error("Error toggling like:", error);
      alert(t('errors.cantLikePost', "Erreur lors du like/unlike."));
    }
  };

  const formatDate = (timestamp: any) => { // Accepte any pour le timestamp simulé
    if (!timestamp || typeof timestamp.seconds !== 'number') return t('blog.unknownDate', 'Date inconnue');
    return new Date(timestamp.seconds * 1000).toLocaleDateString(lang, {
      year: 'numeric', month: 'long', day: 'numeric', hour: '2-digit', minute: '2-digit'
    });
  };


  if (isLoading && posts.length === 0) { // Afficher le loader seulement au chargement initial
    return <LoadingContainer><FiLoader /> {t('loading.blog', 'Chargement du blog...')}</LoadingContainer>;
  }

  if (error) {
    return <ErrorMessage><FiAlertCircle size={30} /><p>{error}</p><Link to="/">{t('actions.backToHome', "Retour à l'accueil")}</Link></ErrorMessage>;
  }

  return (
    <PageContainer>
      <PageHeader>
        <h1><FiMessageSquare /> {t('blog.title', 'Journal Communautaire')}</h1>
        <p>{t('blog.description', 'Partagez vos expériences, découvertes et inspirations avec la communauté PiKnowKyo. Tous les messages sont anonymes.')}</p>
      </PageHeader>

      <ControlsBar>
        <SearchInput
            type="text"
            placeholder={t('blog.searchPlaceholder', "Rechercher des messages...") || "Rechercher..."}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
        />
        <CategorySelect value={selectedCategory} onChange={(e) => setSelectedCategory(e.target.value)}>
            <option value="">{t('blog.allCategories', "Toutes catégories")}</option>
            {ALL_CATEGORIES.map(cat => (
                <option key={cat} value={cat}>{t(`blog.categories.${cat}`, cat.charAt(0).toUpperCase() + cat.slice(1))}</option>
            ))}
        </CategorySelect>
      </ControlsBar>

      {user ? (
        <PostFormCard>
          <SectionTitle style={{borderBottom: 'none', marginBottom: '1rem'}}><FiEdit /> {t('blog.writeNewPost', 'Écrire un nouveau message')}</SectionTitle>
          <TextArea
            placeholder={t('blog.postPlaceholder', "Votre message (sera publié anonymement)...") || "Votre message..."}
            value={newPostContent}
            onChange={e => setNewPostContent(e.target.value)}
            rows={4}
          />
          <FormActions>
            <CategoryInputGroup>
                <label htmlFor="post-category">{t('blog.category', 'Catégorie')}:</label>
                <CategorySelect
                    id="post-category"
                    value={newPostCategory}
                    onChange={(e) => setNewPostCategory(e.target.value)}
                    style={{minWidth: '150px'}}
                >
                    {ALL_CATEGORIES.map(cat => (
                        <option key={cat} value={cat}>{t(`blog.categories.${cat}`, cat.charAt(0).toUpperCase() + cat.slice(1))}</option>
                    ))}
                </CategorySelect>
            </CategoryInputGroup>
            <Button onClick={handlePostSubmit} disabled={isSubmitting || !newPostContent.trim()}>
              {isSubmitting ? <FiLoader style={{animation: 'spin 1s linear infinite'}} /> : <FiSend />}
              {isSubmitting ? t('blog.publishing', 'Publication...') : t('blog.publish', 'Publier')}
            </Button>
          </FormActions>
        </PostFormCard>
      ) : (
        <InfoMessage>
          <FiUserX />
          {t('blog.loginToPost', 'Vous devez être connecté pour publier un message.')}
          {/* <Link to="/profile">Se connecter / S'inscrire</Link>  Ajoutez un lien de connexion */}
        </InfoMessage>
      )}


      {posts.length === 0 && !isLoading && (
        <InfoMessage>
          <FiMessageSquare />
          {t('blog.noPostsYet', 'Aucun message pour le moment dans cette catégorie ou correspondant à votre recherche.')}
        </InfoMessage>
      )}

      {posts.map((post) => (
        <PostCard key={post.id}>
          <PostHeader>
            <AuthorPseudo><FiUser size={16}/> {post.authorPseudo}</AuthorPseudo>
            <PostDate><FiClock size={14}/> {formatDate(post.createdAt)}</PostDate>
          </PostHeader>
          <PostContent>{post.content}</PostContent>
          <PostFooter>
            <ActionButton
                onClick={() => handleLikePost(post.id)}
                $liked={!!(user && post.likes.includes(user.uid))}
                title={t('blog.like', 'Aimer') || 'Aimer'}
            >
              <FiThumbsUp /> {post.likes.length}
            </ActionButton>
            <ActionButton as={Link} to={`/blog/${post.id}/comments`} title={t('blog.comments', 'Commentaires') || 'Commentaires'}> {/* Lien vers une page de commentaires */}
              <FiCommentIcon /> {post.commentCount || 0}
            </ActionButton>
            {/* Ajouter des tags si pertinent */}
            {/* <div>{post.tags.map(tag => `#${tag} `)}</div> */}
          </PostFooter>
        </PostCard>
      ))}
    </PageContainer>
  );
};

export default BlogPage;