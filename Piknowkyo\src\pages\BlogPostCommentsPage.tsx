// src/pages/BlogPostCommentsPage.tsx

import React, { useEffect, useState, useContext } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import styled, { ThemeContext, DefaultTheme } from 'styled-components';
import { useTranslation } from 'react-i18next';
import { useLang } from '../LangProvider';
import { useAuth } from '../contexts/AuthContext';
import { FiMessageSquare, FiSend, FiChevronLeft, FiLoader, FiAlertCircle, FiUser, FiClock } from 'react-icons/fi';
import { BlogService, CommentService, BlogPost as BlogPostType, BlogComment as BlogCommentType, generateAnonymousPseudo } from '../services/blogService';

// --- Types ---
type BlogPost = BlogPostType;
type BlogComment = BlogCommentType;

// --- Styled Components (similaires à BlogPage, à adapter) ---
const PageContainer = styled.div`
  padding: 1.5rem;
  max-width: 700px;
  margin: 0 auto;
  color: ${({ theme }) => theme.text};
`;
const BackButton = styled.button` /* ... (style du BackButton de SessionDetailPage) ... */ `;
const PostDetailCard = styled.div`
  background: ${({ theme }) => theme.surface};
  border-radius: 12px;
  box-shadow: ${({ theme }) => theme.cardShadow};
  padding: 1.5rem;
  margin-bottom: 2rem;
`;
const PostContent = styled.p` /* ... (style de BlogPage) ... */ `;
const AuthorInfo = styled.div` /* ... (style pour l'auteur et la date) ... */ `;

const CommentsSection = styled.section`
  margin-top: 2rem;
  h2 {
    font-size: 1.5rem;
    color: ${({ theme }) => theme.primary};
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid ${({ theme }) => theme.border};
  }
`;
const CommentList = styled.ul`
  list-style: none;
  padding: 0;
`;
const CommentItem = styled.li`
  background: ${({ theme }) => theme.surfaceAlt};
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  border-left: 3px solid ${({ theme }) => theme.accent};
  p { margin: 0.5rem 0 0 0; font-size: 0.95rem; line-height: 1.6; }
  small { font-size: 0.8rem; color: ${({ theme }) => theme.textMuted}; }
`;

const CommentFormCard = styled.div` /* ... (style de PostFormCard de BlogPage) ... */ `;
const TextArea = styled.textarea` /* ... (style de BlogPage) ... */ `;
const Button = styled.button` /* ... (style de BlogPage) ... */ `;

const LoadingContainer = styled.div` /* ... */ `;
const ErrorMessage = styled.div` /* ... */ `;


const BlogPostCommentsPage: React.FC = () => {
  const { t } = useTranslation();
  const { postId } = useParams<{ postId: string }>(); // Récupérer postId de l'URL
  const navigate = useNavigate();
  const { lang } = useLang();
  const { user } = useAuth();

  const [post, setPost] = useState<BlogPost | null>(null);
  const [comments, setComments] = useState<BlogComment[]>([]);
  const [newComment, setNewComment] = useState('');
  const [isLoadingPost, setIsLoadingPost] = useState(true);
  const [isLoadingComments, setIsLoadingComments] = useState(true);
  const [isSubmittingComment, setIsSubmittingComment] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Charger les détails du post
  useEffect(() => {
    if (!postId) {
      setError(t('errors.missingPostId', "ID du message manquant."));
      setIsLoadingPost(false);
      return;
    }

    const loadPost = async () => {
      setIsLoadingPost(true);
      try {
        const postData = await BlogService.getPost(postId);
        if (postData) {
          setPost(postData);
          setError(null);
        } else {
          setError(t('errors.postNotFound', "Message non trouvé."));
        }
      } catch (error) {
        console.error("Error fetching post:", error);
        setError(t('errors.cantLoadPost', "Impossible de charger le message."));
      } finally {
        setIsLoadingPost(false);
      }
    };

    loadPost();
  }, [postId, t]);

  // Charger les commentaires pour ce post
  useEffect(() => {
    if (!postId) return;

    const loadComments = async () => {
      setIsLoadingComments(true);
      try {
        const fetchedComments = await CommentService.getComments(postId);
        setComments(fetchedComments);
      } catch (error) {
        console.error("Error fetching comments:", error);
        setError(t('errors.cantLoadComments', "Impossible de charger les commentaires."));
      } finally {
        setIsLoadingComments(false);
      }
    };

    loadComments();
  }, [postId, t]);

  const handleCommentSubmit = async () => {
    if (!newComment.trim() || !user || !postId) return;
    setIsSubmittingComment(true);

    try {
      await CommentService.createComment(postId, user.uid, newComment);

      // Recharger les commentaires après création
      const fetchedComments = await CommentService.getComments(postId);
      setComments(fetchedComments);

      setNewComment('');
    } catch (error) {
      console.error("Error adding comment:", error);
      alert(t('errors.cantAddComment', "Erreur lors de l'ajout du commentaire."));
    } finally {
      setIsSubmittingComment(false);
    }
  };

  const formatDate = (timestamp: any) => { /* ... (copier depuis BlogPage) ... */ return new Date(timestamp.seconds * 1000).toLocaleDateString(); };


  if (isLoadingPost) return <LoadingContainer><FiLoader /> {t('loading.post', 'Chargement du message...')}</LoadingContainer>;
  if (error) return <ErrorMessage><p>{error}</p><Link to="/blog">{t('actions.backToBlog', 'Retour au blog')}</Link></ErrorMessage>;
  if (!post) return <ErrorMessage><p>{t('errors.postNotFound', 'Message non trouvé.')}</p><Link to="/blog">{t('actions.backToBlog', 'Retour au blog')}</Link></ErrorMessage>;

  return (
    <PageContainer>
      <BackButton onClick={() => navigate('/blog')} title={t('actions.backToBlog', "Retour au blog") || "Retour au blog"}>
        <FiChevronLeft />
      </BackButton>

      <PostDetailCard>
        <AuthorInfo>
            <span><FiUser /> {post.authorPseudo}</span>
            <span><FiClock /> {formatDate(post.createdAt)}</span>
        </AuthorInfo>
        <PostContent>{post.content}</PostContent>
        {/* On pourrait afficher les tags ici aussi */}
      </PostDetailCard>

      <CommentsSection>
        <h2>{t('blog.commentsSectionTitle', 'Commentaires')} ({comments.length})</h2>
        {isLoadingComments && <LoadingContainer><FiLoader /> {t('loading.comments', 'Chargement des commentaires...')}</LoadingContainer>}

        {!isLoadingComments && comments.length === 0 && (
          <p>{t('blog.noCommentsYet', 'Aucun commentaire pour le moment. Soyez le premier à commenter !')}</p>
        )}

        <CommentList>
          {comments.map(comment => (
            <CommentItem key={comment.id}>
              <small><strong>{comment.authorPseudo}</strong> - {formatDate(comment.createdAt)}</small>
              <p>{comment.text}</p>
            </CommentItem>
          ))}
        </CommentList>

        {user ? (
          <CommentFormCard style={{marginTop: '2rem'}}>
            <h3>{t('blog.addComment', 'Ajouter un commentaire')}</h3>
            <TextArea
              rows={3}
              placeholder={t('blog.yourCommentPlaceholder', 'Votre commentaire...') || "Votre commentaire..."}
              value={newComment}
              onChange={(e) => setNewComment(e.target.value)}
            />
            <Button onClick={handleCommentSubmit} disabled={isSubmittingComment || !newComment.trim()}>
              {isSubmittingComment ? <FiLoader style={{animation: 'spin 1s linear infinite'}}/> : <FiSend />}
              {isSubmittingComment ? t('blog.sending', 'Envoi...') : t('blog.sendComment', 'Envoyer')}
            </Button>
          </CommentFormCard>
        ) : (
          <p style={{marginTop: '2rem', textAlign: 'center'}}>
            {t('blog.loginToComment', 'Connectez-vous pour ajouter un commentaire.')}
          </p>
        )}
      </CommentsSection>
    </PageContainer>
  );
};

export default BlogPostCommentsPage;