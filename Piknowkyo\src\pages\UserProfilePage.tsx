import React, { useState } from 'react';
import styled from 'styled-components';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useUserPreferences } from '../hooks/useUserPreferences';
import { FiUser, FiSettings, FiSave, FiRefreshCw, FiCheck, FiX, FiLoader, FiLogOut, FiWifi, FiWifiOff } from 'react-icons/fi';

// Styled Components
const PageContainer = styled.div`
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
  background: ${props => props.theme.background};
  min-height: 100vh;
`;

const ProfileHeader = styled.div`
  background: ${props => props.theme.surface};
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: ${props => props.theme.cardShadow};
  text-align: center;
`;

const Avatar = styled.div`
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: ${props => props.theme.primary};
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem;
  color: ${props => props.theme.textLight};
  font-size: 2rem;
`;

const UserName = styled.h1`
  color: ${props => props.theme.text};
  margin: 0 0 0.5rem;
  font-size: 1.5rem;
`;

const UserEmail = styled.p`
  color: ${props => props.theme.textSecondary};
  margin: 0;
`;

const Section = styled.div`
  background: ${props => props.theme.surface};
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: ${props => props.theme.cardShadow};
`;

const SectionTitle = styled.h2`
  color: ${props => props.theme.text};
  margin: 0 0 1.5rem;
  font-size: 1.25rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const FormGroup = styled.div`
  margin-bottom: 1.5rem;
`;

const Label = styled.label`
  display: block;
  color: ${props => props.theme.text};
  margin-bottom: 0.5rem;
  font-weight: 500;
`;

const Select = styled.select`
  width: 100%;
  padding: 0.75rem;
  border: 1px solid ${props => props.theme.border};
  border-radius: 8px;
  background: ${props => props.theme.inputBackground};
  color: ${props => props.theme.text};
  font-size: 1rem;

  &:focus {
    outline: none;
    border-color: ${props => props.theme.primary};
  }
`;

const CheckboxGroup = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
`;

const Checkbox = styled.input`
  width: 18px;
  height: 18px;
  accent-color: ${props => props.theme.primary};
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 2rem;
  flex-wrap: wrap;
`;

const Button = styled.button<{ variant?: 'primary' | 'secondary' | 'danger' }>`
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s ease;

  ${props => {
    switch (props.variant) {
      case 'primary':
        return `
          background: ${props.theme.primary};
          color: ${props.theme.textLight};
          &:hover { background: ${props.theme.primaryDark}; }
        `;
      case 'danger':
        return `
          background: #EF4444;
          color: white;
          &:hover { background: #DC2626; }
        `;
      default:
        return `
          background: ${props.theme.secondary};
          color: ${props.theme.textLight};
          &:hover { background: ${props.theme.secondaryDark}; }
        `;
    }
  }}

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
`;

const SyncStatusBar = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: ${props => props.theme.surfaceAlt};
  border-radius: 8px;
  margin-bottom: 1rem;
  font-size: 0.9rem;
`;

const SyncIcon = styled.div<{ $status: 'fresh' | 'recent' | 'stale' | 'pending' | 'syncing' | 'error' | 'not-authenticated' }>`
  color: ${({ $status }) => {
    switch ($status) {
      case 'fresh': return '#10B981';
      case 'recent': return '#10B981';
      case 'pending': return '#F59E0B';
      case 'syncing': return '#3B82F6';
      case 'error': return '#EF4444';
      case 'stale': return '#F59E0B';
      default: return '#6B7280';
    }
  }};
  
  ${({ $status }) => $status === 'syncing' && `
    animation: spin 1s linear infinite;
    @keyframes spin {
      from { transform: rotate(0deg); }
      to { transform: rotate(360deg); }
    }
  `}
`;

const LoadingSpinner = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem;
  color: ${props => props.theme.textSecondary};
`;

const ErrorMessage = styled.div`
  background: ${props => props.theme.errorColor};
  color: white;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
`;

const SuccessMessage = styled.div`
  background: ${props => props.theme.success};
  color: white;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
`;

const UserProfilePage: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { user, signOut } = useAuth();
  const {
    preferences,
    isLoading,
    error,
    hasUnsavedChanges,
    syncStatus,
    updatePreference,
    updateLanguage,
    updateTheme,
    savePreferences,
    resetToDefaults,
  } = useUserPreferences();

  const [localError, setLocalError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [isSigningOut, setIsSigningOut] = useState(false);

  const handleSignOut = async () => {
    setIsSigningOut(true);
    try {
      await signOut();
      navigate('/');
    } catch (error) {
      console.error('Erreur lors de la déconnexion:', error);
      setLocalError(t('profile.signOutError', 'Erreur lors de la déconnexion'));
    } finally {
      setIsSigningOut(false);
    }
  };

  const handleSave = async () => {
    const success = await savePreferences();
    if (success) {
      setSuccess(t('profile.preferencesSaved', 'Préférences sauvegardées avec succès'));
      setTimeout(() => setSuccess(null), 3000);
    } else {
      setLocalError(t('profile.errorSavingPreferences', 'Erreur lors de la sauvegarde des préférences'));
    }
  };

  const handleReset = async () => {
    const success = await resetToDefaults();
    if (success) {
      setSuccess(t('profile.preferencesReset', 'Préférences réinitialisées'));
      setTimeout(() => setSuccess(null), 3000);
    } else {
      setLocalError(t('profile.errorResettingPreferences', 'Erreur lors de la réinitialisation'));
    }
  };

  const getSyncStatusMessage = () => {
    switch (syncStatus) {
      case 'fresh': return t('sync.fresh', 'Synchronisé');
      case 'recent': return t('sync.recent', 'Récemment synchronisé');
      case 'pending': return t('sync.pending', 'Changements en attente');
      case 'syncing': return t('sync.syncing', 'Synchronisation...');
      case 'error': return t('sync.error', 'Erreur de synchronisation');
      case 'stale': return t('sync.stale', 'Synchronisation recommandée');
      default: return t('sync.notAuthenticated', 'Non connecté');
    }
  };

  const getSyncIcon = () => {
    switch (syncStatus) {
      case 'syncing': return <FiRefreshCw />;
      case 'error': return <FiWifiOff />;
      default: return <FiWifi />;
    }
  };

  if (!user) {
    return (
      <PageContainer>
        <ErrorMessage>
          {t('profile.notLoggedIn', 'Vous devez être connecté pour accéder à votre profil')}
        </ErrorMessage>
      </PageContainer>
    );
  }

  if (isLoading) {
    return (
      <PageContainer>
        <LoadingSpinner>
          <FiLoader style={{ animation: 'spin 1s linear infinite' }} />
          {t('profile.loading', 'Chargement du profil...')}
        </LoadingSpinner>
      </PageContainer>
    );
  }

  return (
    <PageContainer>
      {(error || localError) && <ErrorMessage>{error || localError}</ErrorMessage>}
      {success && <SuccessMessage><FiCheck /> {success}</SuccessMessage>}

      <ProfileHeader>
        <Avatar>
          <FiUser />
        </Avatar>
        <UserName>{user.displayName || t('profile.anonymousUser', 'Utilisateur Anonyme')}</UserName>
        <UserEmail>{user.email}</UserEmail>
      </ProfileHeader>

      <Section>
        <SectionTitle>
          <FiSettings />
          {t('profile.generalSettings', 'Paramètres généraux')}
        </SectionTitle>

        <SyncStatusBar>
          <SyncIcon $status={syncStatus}>
            {getSyncIcon()}
          </SyncIcon>
          <span>{getSyncStatusMessage()}</span>
          {hasUnsavedChanges && <span>• {t('sync.unsavedChanges', 'Modifications non sauvegardées')}</span>}
        </SyncStatusBar>

        <FormGroup>
          <Label>{t('profile.language', 'Langue')}</Label>
          <Select
            value={preferences.language}
            onChange={(e) => updateLanguage(e.target.value as 'fr' | 'en' | 'es')}
          >
            <option value="fr">Français</option>
            <option value="en">English</option>
            <option value="es">Español</option>
          </Select>
        </FormGroup>

        <FormGroup>
          <Label>{t('profile.theme', 'Thème')}</Label>
          <Select
            value={preferences.theme}
            onChange={(e) => updateTheme(e.target.value as 'light' | 'dark')}
          >
            <option value="light">{t('profile.lightTheme', 'Clair')}</option>
            <option value="dark">{t('profile.darkTheme', 'Sombre')}</option>
          </Select>
        </FormGroup>

        <CheckboxGroup>
          <Checkbox
            type="checkbox"
            checked={preferences.notifications}
            onChange={(e) => updatePreference('notifications', e.target.checked)}
          />
          <Label>{t('profile.enableNotifications', 'Activer les notifications')}</Label>
        </CheckboxGroup>

        <CheckboxGroup>
          <Checkbox
            type="checkbox"
            checked={preferences.soundEnabled}
            onChange={(e) => updatePreference('soundEnabled', e.target.checked)}
          />
          <Label>{t('profile.enableSound', 'Activer le son')}</Label>
        </CheckboxGroup>
      </Section>

      <ButtonGroup>
        <Button variant="danger" onClick={handleSignOut} disabled={isSigningOut}>
          {isSigningOut ? <FiLoader style={{ animation: 'spin 1s linear infinite' }} /> : <FiLogOut />}
          {isSigningOut ? t('profile.signingOut', 'Déconnexion...') : t('profile.signOut', 'Se déconnecter')}
        </Button>
        <Button variant="secondary" onClick={handleReset} disabled={isLoading}>
          <FiRefreshCw />
          {t('profile.resetToDefaults', 'Réinitialiser')}
        </Button>
        <Button variant="primary" onClick={handleSave} disabled={isLoading || !hasUnsavedChanges}>
          {isLoading ? <FiLoader style={{ animation: 'spin 1s linear infinite' }} /> : <FiSave />}
          {isLoading ? t('profile.saving', 'Sauvegarde...') : t('profile.save', 'Sauvegarder')}
        </Button>
      </ButtonGroup>
    </PageContainer>
  );
};

export default UserProfilePage;
