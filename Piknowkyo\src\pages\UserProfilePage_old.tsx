import React, { useState, useEffect, useContext } from 'react';
import styled, { ThemeContext, DefaultTheme } from 'styled-components';
import { useTranslation } from 'react-i18next';
import { useAuth } from '../contexts/AuthContext';
import { useUserPreferences } from '../hooks/useUserPreferences';
import { UserPreferences } from '../services/userPreferencesService';
import { FiUser, FiSettings, FiSave, FiRefreshCw, FiCheck, FiX, FiLoader, FiLogOut, FiWifi, FiWifiOff } from 'react-icons/fi';

// Styled Components
const PageContainer = styled.div`
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
  background: ${props => props.theme.background};
  min-height: 100vh;
`;

const ProfileHeader = styled.div`
  background: ${props => props.theme.surface};
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: ${props => props.theme.cardShadow};
  text-align: center;
`;

const Avatar = styled.div`
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: ${props => props.theme.primary};
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem;
  color: ${props => props.theme.textLight};
  font-size: 2rem;
`;

const UserName = styled.h1`
  color: ${props => props.theme.text};
  margin: 0 0 0.5rem;
  font-size: 1.5rem;
`;

const UserEmail = styled.p`
  color: ${props => props.theme.textSecondary};
  margin: 0;
`;

const Section = styled.div`
  background: ${props => props.theme.surface};
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: ${props => props.theme.cardShadow};
`;

const SectionTitle = styled.h2`
  color: ${props => props.theme.text};
  margin: 0 0 1.5rem;
  font-size: 1.25rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const FormGroup = styled.div`
  margin-bottom: 1.5rem;
`;

const Label = styled.label`
  display: block;
  color: ${props => props.theme.text};
  margin-bottom: 0.5rem;
  font-weight: 500;
`;

const Select = styled.select`
  width: 100%;
  padding: 0.75rem;
  border: 1px solid ${props => props.theme.border};
  border-radius: 8px;
  background: ${props => props.theme.inputBackground};
  color: ${props => props.theme.text};
  font-size: 1rem;

  &:focus {
    outline: none;
    border-color: ${props => props.theme.primary};
  }
`;

const CheckboxGroup = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
`;

const Checkbox = styled.input`
  width: 18px;
  height: 18px;
  accent-color: ${props => props.theme.primary};
`;

const RangeGroup = styled.div`
  margin-bottom: 1rem;
`;

const RangeInput = styled.input`
  width: 100%;
  margin: 0.5rem 0;
  accent-color: ${props => props.theme.primary};
`;

const RangeValue = styled.span`
  color: ${props => props.theme.textSecondary};
  font-size: 0.9rem;
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 2rem;
`;

const Button = styled.button<{ variant?: 'primary' | 'secondary' }>`
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s ease;

  ${props => props.variant === 'primary' ? `
    background: ${props.theme.primary};
    color: ${props.theme.textLight};
    
    &:hover {
      background: ${props.theme.primaryDark};
    }
  ` : `
    background: ${props.theme.secondary};
    color: ${props.theme.textLight};
    
    &:hover {
      background: ${props.theme.secondaryDark};
    }
  `}

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
`;

const LoadingSpinner = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem;
  color: ${props => props.theme.textSecondary};
`;

const ErrorMessage = styled.div`
  background: ${props => props.theme.errorColor};
  color: white;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
`;

const SuccessMessage = styled.div`
  background: ${props => props.theme.success};
  color: white;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
`;

const UserProfilePage: React.FC = () => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const [preferences, setPreferences] = useState<UserPreferences>(defaultPreferences);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Charger les préférences utilisateur
  useEffect(() => {
    if (user) {
      loadUserPreferences();
    }
  }, [user]);

  const loadUserPreferences = async () => {
    if (!user) return;
    
    setLoading(true);
    try {
      const userPrefs = await UserPreferencesService.getUserPreferences(user.uid);
      setPreferences(userPrefs);
      setError(null);
    } catch (error) {
      console.error('Erreur lors du chargement des préférences:', error);
      setError(t('profile.errorLoadingPreferences', 'Erreur lors du chargement des préférences'));
    } finally {
      setLoading(false);
    }
  };

  const savePreferences = async () => {
    if (!user) return;

    setSaving(true);
    try {
      await UserPreferencesService.updateUserPreferences(user.uid, preferences);
      setSuccess(t('profile.preferencesSaved', 'Préférences sauvegardées avec succès'));
      setError(null);
      setTimeout(() => setSuccess(null), 3000);
    } catch (error) {
      console.error('Erreur lors de la sauvegarde:', error);
      setError(t('profile.errorSavingPreferences', 'Erreur lors de la sauvegarde des préférences'));
    } finally {
      setSaving(false);
    }
  };

  const resetToDefaults = async () => {
    if (!user) return;

    setSaving(true);
    try {
      await UserPreferencesService.resetToDefaults(user.uid);
      setPreferences(defaultPreferences);
      setSuccess(t('profile.preferencesReset', 'Préférences réinitialisées'));
      setError(null);
      setTimeout(() => setSuccess(null), 3000);
    } catch (error) {
      console.error('Erreur lors de la réinitialisation:', error);
      setError(t('profile.errorResettingPreferences', 'Erreur lors de la réinitialisation'));
    } finally {
      setSaving(false);
    }
  };

  const updatePreference = (key: keyof UserPreferences, value: any) => {
    setPreferences(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const updateNestedPreference = (section: keyof UserPreferences, key: string, value: any) => {
    setPreferences(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [key]: value
      }
    }));
  };

  if (!user) {
    return (
      <PageContainer>
        <ErrorMessage>
          {t('profile.notLoggedIn', 'Vous devez être connecté pour accéder à votre profil')}
        </ErrorMessage>
      </PageContainer>
    );
  }

  if (loading) {
    return (
      <PageContainer>
        <LoadingSpinner>
          <FiLoader style={{ animation: 'spin 1s linear infinite' }} />
          {t('profile.loading', 'Chargement du profil...')}
        </LoadingSpinner>
      </PageContainer>
    );
  }

  return (
    <PageContainer>
      {error && <ErrorMessage>{error}</ErrorMessage>}
      {success && <SuccessMessage><FiCheck /> {success}</SuccessMessage>}

      <ProfileHeader>
        <Avatar>
          <FiUser />
        </Avatar>
        <UserName>{user.displayName || t('profile.anonymousUser', 'Utilisateur Anonyme')}</UserName>
        <UserEmail>{user.email}</UserEmail>
      </ProfileHeader>

      <Section>
        <SectionTitle>
          <FiSettings />
          {t('profile.generalSettings', 'Paramètres généraux')}
        </SectionTitle>

        <FormGroup>
          <Label>{t('profile.language', 'Langue')}</Label>
          <Select
            value={preferences.language}
            onChange={(e) => updatePreference('language', e.target.value as 'fr' | 'en' | 'es')}
          >
            <option value="fr">Français</option>
            <option value="en">English</option>
            <option value="es">Español</option>
          </Select>
        </FormGroup>

        <FormGroup>
          <Label>{t('profile.theme', 'Thème')}</Label>
          <Select
            value={preferences.theme}
            onChange={(e) => updatePreference('theme', e.target.value as 'light' | 'dark')}
          >
            <option value="light">{t('profile.lightTheme', 'Clair')}</option>
            <option value="dark">{t('profile.darkTheme', 'Sombre')}</option>
          </Select>
        </FormGroup>

        <CheckboxGroup>
          <Checkbox
            type="checkbox"
            checked={preferences.notifications}
            onChange={(e) => updatePreference('notifications', e.target.checked)}
          />
          <Label>{t('profile.enableNotifications', 'Activer les notifications')}</Label>
        </CheckboxGroup>

        <CheckboxGroup>
          <Checkbox
            type="checkbox"
            checked={preferences.soundEnabled}
            onChange={(e) => updatePreference('soundEnabled', e.target.checked)}
          />
          <Label>{t('profile.enableSound', 'Activer le son')}</Label>
        </CheckboxGroup>
      </Section>

      <ButtonGroup>
        <Button variant="secondary" onClick={resetToDefaults} disabled={saving}>
          <FiRefreshCw />
          {t('profile.resetToDefaults', 'Réinitialiser')}
        </Button>
        <Button variant="primary" onClick={savePreferences} disabled={saving}>
          {saving ? <FiLoader style={{ animation: 'spin 1s linear infinite' }} /> : <FiSave />}
          {saving ? t('profile.saving', 'Sauvegarde...') : t('profile.save', 'Sauvegarder')}
        </Button>
      </ButtonGroup>
    </PageContainer>
  );
};

export default UserProfilePage;
