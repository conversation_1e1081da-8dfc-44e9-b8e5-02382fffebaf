import { 
  collection, 
  addDoc, 
  getDocs, 
  doc, 
  updateDoc, 
  deleteDoc, 
  query, 
  orderBy, 
  limit, 
  where, 
  serverTimestamp,
  arrayUnion,
  arrayRemove,
  increment,
  getDoc
} from 'firebase/firestore';
import { db } from './firebase';

// Types pour le blog
export interface BlogPost {
  id: string;
  authorId: string;
  authorPseudo: string;
  content: string;
  category: string;
  tags: string[];
  likes: string[]; // Array des IDs utilisateurs qui ont liké
  commentCount: number;
  createdAt: { seconds: number; nanoseconds: number };
}

export interface BlogComment {
  id: string;
  postId: string;
  authorId: string;
  authorPseudo: string;
  content: string;
  text: string; // Alias pour content pour compatibilité
  likes: string[];
  createdAt: { seconds: number; nanoseconds: number };
}

// Fonction pour générer un pseudo anonyme basé sur l'ID utilisateur
export const generateAnonymousPseudo = (userId: string): string => {
  const adjectives = ['Sage', 'Zen', 'Calme', 'Serein', 'Paisible', 'Lumineux', 'Éveillé', 'Harmonieux'];
  const nouns = ['Esprit', 'Âme', 'Voyageur', 'Chercheur', 'Méditant', 'Penseur', 'Rêveur', 'Guide'];
  
  // Utiliser l'ID utilisateur pour générer un hash simple
  let hash = 0;
  for (let i = 0; i < userId.length; i++) {
    const char = userId.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convertir en 32bit integer
  }
  
  const adjIndex = Math.abs(hash) % adjectives.length;
  const nounIndex = Math.abs(hash >> 8) % nouns.length;
  const number = Math.abs(hash >> 16) % 1000;
  
  return `${adjectives[adjIndex]}${nouns[nounIndex]}${number}`;
};

// Service pour les posts de blog
export class BlogService {
  private static readonly COLLECTION_NAME = 'blogPosts';

  // Créer un nouveau post
  static async createPost(
    authorId: string,
    content: string,
    category: string,
    tags: string[] = []
  ): Promise<string> {
    try {
      const authorPseudo = generateAnonymousPseudo(authorId);
      
      const postData = {
        authorId,
        authorPseudo,
        content,
        category,
        tags,
        likes: [],
        commentCount: 0,
        createdAt: serverTimestamp()
      };

      const docRef = await addDoc(collection(db, this.COLLECTION_NAME), postData);
      return docRef.id;
    } catch (error) {
      console.error('Erreur lors de la création du post:', error);
      throw error;
    }
  }

  // Récupérer tous les posts avec pagination
  static async getPosts(limitCount: number = 20, category?: string): Promise<BlogPost[]> {
    try {
      let q = query(
        collection(db, this.COLLECTION_NAME),
        orderBy('createdAt', 'desc'),
        limit(limitCount)
      );

      if (category && category !== 'all') {
        q = query(
          collection(db, this.COLLECTION_NAME),
          where('category', '==', category),
          orderBy('createdAt', 'desc'),
          limit(limitCount)
        );
      }

      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as BlogPost));
    } catch (error) {
      console.error('Erreur lors de la récupération des posts:', error);
      throw error;
    }
  }

  // Récupérer un post spécifique
  static async getPost(postId: string): Promise<BlogPost | null> {
    try {
      const docRef = doc(db, this.COLLECTION_NAME, postId);
      const docSnap = await getDoc(docRef);
      
      if (docSnap.exists()) {
        return {
          id: docSnap.id,
          ...docSnap.data()
        } as BlogPost;
      }
      return null;
    } catch (error) {
      console.error('Erreur lors de la récupération du post:', error);
      throw error;
    }
  }

  // Liker/Unliker un post
  static async toggleLike(postId: string, userId: string): Promise<void> {
    try {
      const postRef = doc(db, this.COLLECTION_NAME, postId);
      const postSnap = await getDoc(postRef);
      
      if (postSnap.exists()) {
        const postData = postSnap.data() as BlogPost;
        const hasLiked = postData.likes.includes(userId);
        
        if (hasLiked) {
          // Retirer le like
          await updateDoc(postRef, {
            likes: arrayRemove(userId)
          });
        } else {
          // Ajouter le like
          await updateDoc(postRef, {
            likes: arrayUnion(userId)
          });
        }
      }
    } catch (error) {
      console.error('Erreur lors du toggle like:', error);
      throw error;
    }
  }

  // Supprimer un post (seulement par l'auteur)
  static async deletePost(postId: string, userId: string): Promise<void> {
    try {
      const postRef = doc(db, this.COLLECTION_NAME, postId);
      const postSnap = await getDoc(postRef);
      
      if (postSnap.exists()) {
        const postData = postSnap.data() as BlogPost;
        if (postData.authorId === userId) {
          await deleteDoc(postRef);
          // TODO: Supprimer aussi tous les commentaires associés
        } else {
          throw new Error('Vous ne pouvez supprimer que vos propres posts');
        }
      }
    } catch (error) {
      console.error('Erreur lors de la suppression du post:', error);
      throw error;
    }
  }

  // Incrémenter le compteur de commentaires
  static async incrementCommentCount(postId: string): Promise<void> {
    try {
      const postRef = doc(db, this.COLLECTION_NAME, postId);
      await updateDoc(postRef, {
        commentCount: increment(1)
      });
    } catch (error) {
      console.error('Erreur lors de l\'incrémentation du compteur de commentaires:', error);
      throw error;
    }
  }

  // Décrémenter le compteur de commentaires
  static async decrementCommentCount(postId: string): Promise<void> {
    try {
      const postRef = doc(db, this.COLLECTION_NAME, postId);
      await updateDoc(postRef, {
        commentCount: increment(-1)
      });
    } catch (error) {
      console.error('Erreur lors de la décrémentation du compteur de commentaires:', error);
      throw error;
    }
  }
}

// Service pour les commentaires
export class CommentService {
  private static readonly COLLECTION_NAME = 'blogComments';

  // Créer un nouveau commentaire
  static async createComment(
    postId: string,
    authorId: string,
    content: string
  ): Promise<string> {
    try {
      const authorPseudo = generateAnonymousPseudo(authorId);
      
      const commentData = {
        postId,
        authorId,
        authorPseudo,
        content,
        text: content, // Alias pour compatibilité
        likes: [],
        createdAt: serverTimestamp()
      };

      const docRef = await addDoc(collection(db, this.COLLECTION_NAME), commentData);
      
      // Incrémenter le compteur de commentaires du post
      await BlogService.incrementCommentCount(postId);
      
      return docRef.id;
    } catch (error) {
      console.error('Erreur lors de la création du commentaire:', error);
      throw error;
    }
  }

  // Récupérer les commentaires d'un post
  static async getComments(postId: string): Promise<BlogComment[]> {
    try {
      const q = query(
        collection(db, this.COLLECTION_NAME),
        where('postId', '==', postId),
        orderBy('createdAt', 'asc')
      );

      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as BlogComment));
    } catch (error) {
      console.error('Erreur lors de la récupération des commentaires:', error);
      throw error;
    }
  }

  // Liker/Unliker un commentaire
  static async toggleLike(commentId: string, userId: string): Promise<void> {
    try {
      const commentRef = doc(db, this.COLLECTION_NAME, commentId);
      const commentSnap = await getDoc(commentRef);
      
      if (commentSnap.exists()) {
        const commentData = commentSnap.data() as BlogComment;
        const hasLiked = commentData.likes.includes(userId);
        
        if (hasLiked) {
          await updateDoc(commentRef, {
            likes: arrayRemove(userId)
          });
        } else {
          await updateDoc(commentRef, {
            likes: arrayUnion(userId)
          });
        }
      }
    } catch (error) {
      console.error('Erreur lors du toggle like du commentaire:', error);
      throw error;
    }
  }

  // Supprimer un commentaire (seulement par l'auteur)
  static async deleteComment(commentId: string, userId: string, postId: string): Promise<void> {
    try {
      const commentRef = doc(db, this.COLLECTION_NAME, commentId);
      const commentSnap = await getDoc(commentRef);
      
      if (commentSnap.exists()) {
        const commentData = commentSnap.data() as BlogComment;
        if (commentData.authorId === userId) {
          await deleteDoc(commentRef);
          // Décrémenter le compteur de commentaires du post
          await BlogService.decrementCommentCount(postId);
        } else {
          throw new Error('Vous ne pouvez supprimer que vos propres commentaires');
        }
      }
    } catch (error) {
      console.error('Erreur lors de la suppression du commentaire:', error);
      throw error;
    }
  }
}
