import { initializeApp } from 'firebase/app';
import { getFirestore, connectFirestoreEmulator } from 'firebase/firestore';
import { getAuth, connectAuthEmulator } from 'firebase/auth';
import { getStorage, connectStorageEmulator } from 'firebase/storage';

// Configuration Firebase - utilise les variables d'environnement ou les valeurs par défaut
const firebaseConfig = {
  apiKey: import.meta.env.VITE_FIREBASE_API_KEY || "AIzaSyABy8bGDxVU-sM2nqfsp3jDm8JPtg_v4kM",
  authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN || "piknowkyo-777.firebaseapp.com",
  projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID || "piknowkyo-777",
  storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET || "piknowkyo-777.firebasestorage.app",
  messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID || "375619599814",
  appId: import.meta.env.VITE_FIREBASE_APP_ID || "1:375619599814:web:9ece9c5c2ce600a8c206c7",
  measurementId: import.meta.env.VITE_FIREBASE_MEASUREMENT_ID || "G-DSXRMZ4JP2"
};

// Initialiser Firebase
const app = initializeApp(firebaseConfig);

// Initialiser Firestore
const db = getFirestore(app);

const auth = getAuth(app);
const storage = getStorage(app);

// Connecter aux émulateurs en développement
if (import.meta.env.DEV && import.meta.env.VITE_USE_FIREBASE_EMULATOR === 'true') {
  try {
    connectFirestoreEmulator(db, 'localhost', 8080);
    connectAuthEmulator(auth, 'http://localhost:9099');
    connectStorageEmulator(storage, 'localhost', 9199);
    console.log('Connected to Firebase emulators');
  } catch (error) {
    console.warn('Firebase emulators already connected or not available');
  }
}

export { db, auth, storage };
