import { doc, getDoc, setDoc, updateDoc, serverTimestamp } from 'firebase/firestore';
import { db } from '../firebase';

// Interface pour la configuration des prix
export interface PricingConfig {
  premiumPrice: number;
  premiumCurrency: string;
  trialDays: number;
  features: {
    free: string[];
    premium: string[];
  };
  updatedAt?: any;
}

// Configuration par défaut
const defaultPricingConfig: PricingConfig = {
  premiumPrice: 4.99,
  premiumCurrency: '€',
  trialDays: 14,
  features: {
    free: [
      'Méditations guidées de base',
      'Histoires relaxantes',
      'Musique de fond',
      'Voix de base',
      'Accès limité aux jeux'
    ],
    premium: [
      'Toutes les méditations',
      'Toutes les histoires',
      'Sons binauraux avancés',
      'Voix premium',
      'Accès illimité aux jeux',
      'Personnalisation avancée',
      'Support prioritaire',
      'Synchronisation multi-appareils'
    ]
  }
};

export class PricingService {
  private static readonly COLLECTION_NAME = 'config';
  private static readonly DOC_ID = 'pricing';

  // Récupérer la configuration des prix
  static async getPricingConfig(): Promise<PricingConfig> {
    try {
      const docRef = doc(db, this.COLLECTION_NAME, this.DOC_ID);
      const docSnap = await getDoc(docRef);
      
      if (docSnap.exists()) {
        const data = docSnap.data();
        return {
          ...defaultPricingConfig,
          ...data
        } as PricingConfig;
      } else {
        // Créer la configuration par défaut si elle n'existe pas
        await this.createDefaultPricingConfig();
        return defaultPricingConfig;
      }
    } catch (error) {
      console.error('Erreur lors de la récupération de la configuration des prix:', error);
      return defaultPricingConfig;
    }
  }

  // Créer la configuration par défaut
  static async createDefaultPricingConfig(): Promise<void> {
    try {
      const docRef = doc(db, this.COLLECTION_NAME, this.DOC_ID);
      await setDoc(docRef, {
        ...defaultPricingConfig,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      });
      console.log('Configuration des prix par défaut créée');
    } catch (error) {
      console.error('Erreur lors de la création de la configuration par défaut:', error);
      throw error;
    }
  }

  // Mettre à jour la configuration des prix (pour l'admin)
  static async updatePricingConfig(config: Partial<PricingConfig>): Promise<void> {
    try {
      const docRef = doc(db, this.COLLECTION_NAME, this.DOC_ID);
      await updateDoc(docRef, {
        ...config,
        updatedAt: serverTimestamp()
      });
      console.log('Configuration des prix mise à jour');
    } catch (error) {
      console.error('Erreur lors de la mise à jour de la configuration:', error);
      throw error;
    }
  }

  // Mettre à jour seulement le prix premium
  static async updatePremiumPrice(price: number, currency: string): Promise<void> {
    try {
      const docRef = doc(db, this.COLLECTION_NAME, this.DOC_ID);
      await updateDoc(docRef, {
        premiumPrice: price,
        premiumCurrency: currency,
        updatedAt: serverTimestamp()
      });
      console.log(`Prix premium mis à jour: ${price}${currency}`);
    } catch (error) {
      console.error('Erreur lors de la mise à jour du prix premium:', error);
      throw error;
    }
  }

  // Obtenir seulement le prix premium
  static async getPremiumPrice(): Promise<{ price: number; currency: string }> {
    try {
      const config = await this.getPricingConfig();
      return {
        price: config.premiumPrice,
        currency: config.premiumCurrency
      };
    } catch (error) {
      console.error('Erreur lors de la récupération du prix premium:', error);
      return {
        price: defaultPricingConfig.premiumPrice,
        currency: defaultPricingConfig.premiumCurrency
      };
    }
  }

  // Vérifier si la configuration existe
  static async configExists(): Promise<boolean> {
    try {
      const docRef = doc(db, this.COLLECTION_NAME, this.DOC_ID);
      const docSnap = await getDoc(docRef);
      return docSnap.exists();
    } catch (error) {
      console.error('Erreur lors de la vérification de la configuration:', error);
      return false;
    }
  }
}
