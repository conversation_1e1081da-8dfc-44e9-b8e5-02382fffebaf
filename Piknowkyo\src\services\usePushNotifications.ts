import { useEffect } from 'react';
import { Capacitor } from '@capacitor/core';
import { PushNotifications } from '@capacitor/push-notifications';

// Fonction pour vérifier si on est sur le web
const isWeb = () => {
  return Capacitor.getPlatform() === 'web';
};

// Interface commune pour les notifications
interface NotificationOptions {
  title: string;
  body: string;
  icon?: string;
  data?: any;
}

// Implémentation pour le web
class WebPushNotifications {
  static async requestPermissions() {
    if (!('Notification' in window)) {
      console.warn('Ce navigateur ne supporte pas les notifications web');
      return { receive: 'denied' };
    }

    // Vérifier si les permissions sont déjà bloquées
    if (Notification.permission === 'denied') {
      console.warn('Les notifications ont été bloquées par l\'utilisateur. Réinitialisez les permissions dans les paramètres du navigateur.');
      return { receive: 'denied' };
    }

    // Si déjà accordées, ne pas redemander
    if (Notification.permission === 'granted') {
      return { receive: 'granted' };
    }

    // Demander la permission seulement si elle n'a pas été définie
    try {
      const permission = await Notification.requestPermission();
      return { receive: permission };
    } catch (error) {
      console.error('Erreur lors de la demande de permission de notification:', error);
      return { receive: 'denied' };
    }
  }

  static async register() {
    // Pour le web, l'enregistrement est plus complexe et nécessite un service worker
    // Cette implémentation est simplifiée
    console.log('Web notifications registered');
    return;
  }

  static showNotification(options: NotificationOptions) {
    if (!('Notification' in window)) {
      console.warn('Ce navigateur ne supporte pas les notifications web');
      return;
    }

    if (Notification.permission === 'granted') {
      new Notification(options.title, {
        body: options.body,
        icon: options.icon,
        data: options.data
      });
    }
  }

  // Méthodes pour simuler l'API Capacitor
  static addListener(event: string, callback: any) {
    // Pour le web, on ne peut pas vraiment écouter les événements de la même façon
    // On pourrait implémenter cela avec un service worker
    console.log(`Web notification listener added for ${event}`);
    return {
      remove: () => console.log(`Web notification listener removed for ${event}`)
    };
  }
}

export function usePushNotifications() {
  useEffect(() => {
    // Ne pas demander automatiquement les permissions
    // Les permissions seront demandées seulement quand l'utilisateur active les notifications

    if (isWeb()) {
      // Vérifier le statut actuel des permissions sans les demander
      if (Notification.permission === 'granted') {
        WebPushNotifications.register();
      }

      // Pour le web, on n'a pas besoin d'ajouter des listeners comme avec Capacitor
      // car les notifications sont gérées différemment
    } else {
      // Pour les plateformes mobiles, vérifier les permissions existantes
      PushNotifications.checkPermissions().then(result => {
        if (result.receive === 'granted') {
          PushNotifications.register();
        }
      }).catch(err => {
        console.log('Permissions check failed:', err);
      });

      PushNotifications.addListener('registration', token => {
        // Enregistre le token pour l'utilisateur si besoin
        console.log('Push registration success, token:', token.value);
      });

      PushNotifications.addListener('registrationError', err => {
        console.error('Push registration error:', err);
      });

      PushNotifications.addListener('pushNotificationReceived', notification => {
        alert('Notification reçue: ' + notification.title + '\n' + notification.body);
      });

      PushNotifications.addListener('pushNotificationActionPerformed', action => {
        // Gère l'action sur la notification
        console.log('Notification action performed', action);
      });
    }
  }, []);
}

// Fonction pour demander explicitement les permissions
export async function requestNotificationPermissions() {
  if (isWeb()) {
    return await WebPushNotifications.requestPermissions();
  } else {
    return await PushNotifications.requestPermissions();
  }
}

// Exporter une fonction pour envoyer des notifications (utile pour les tests)
export function sendNotification(options: NotificationOptions) {
  if (isWeb()) {
    WebPushNotifications.showNotification(options);
  } else {
    // Pour les plateformes mobiles, cela nécessiterait un backend
    console.log('Sending push notification on mobile requires a backend service');
  }
}
