import React from 'react';
import {
  doc,
  updateDoc,
  getDoc,
  setDoc,
  serverTimestamp
} from 'firebase/firestore';
import { db } from './firebase';

// Types pour les préférences utilisateur
export interface UserPreferences {
  language: 'fr' | 'en' | 'es';
  theme: 'light' | 'dark';
  notifications: boolean;
  soundEnabled: boolean;
  hapticFeedback: boolean;
  autoSaveGames: boolean;
  privacyMode: boolean;
  emailNotifications: boolean;
  pushNotifications: boolean;
  binaural: {
    baseFrequency: number;
    organFrequency: number;
    consciousnessState: 'alpha' | 'beta' | 'theta' | 'delta';
    volume: number;
    enabled: boolean;
  };
  sessions: {
    autoPlay: boolean;
    defaultDuration: number;
    reminderEnabled: boolean;
    reminderTime: string; // Format HH:MM
  };
  accessibility: {
    fontSize: 'small' | 'medium' | 'large';
    highContrast: boolean;
    reducedMotion: boolean;
  };
}

// Préférences par défaut
export const defaultPreferences: UserPreferences = {
  language: 'fr',
  theme: 'light',
  notifications: true,
  soundEnabled: true,
  hapticFeedback: true,
  autoSaveGames: true,
  privacyMode: false,
  emailNotifications: true,
  pushNotifications: true,
  binaural: {
    baseFrequency: 40,
    organFrequency: 528,
    consciousnessState: 'alpha',
    volume: 0.5,
    enabled: true
  },
  sessions: {
    autoPlay: false,
    defaultDuration: 15,
    reminderEnabled: false,
    reminderTime: '20:00'
  },
  accessibility: {
    fontSize: 'medium',
    highContrast: false,
    reducedMotion: false
  }
};

// Service pour la gestion des préférences utilisateur
export class UserPreferencesService {
  private static readonly COLLECTION_NAME = 'userPreferences';

  // Récupérer les préférences d'un utilisateur
  static async getUserPreferences(userId: string): Promise<UserPreferences> {
    try {
      const docRef = doc(db, this.COLLECTION_NAME, userId);
      const docSnap = await getDoc(docRef);
      
      if (docSnap.exists()) {
        const data = docSnap.data();
        // Supprimer les champs Firebase non-sérialisables
        const { createdAt, updatedAt, ...cleanData } = data;
        
        // Fusionner avec les préférences par défaut pour s'assurer que toutes les propriétés existent
        return {
          ...defaultPreferences,
          ...cleanData
        } as UserPreferences;
      } else {
        // Créer les préférences par défaut si elles n'existent pas
        await this.createDefaultPreferences(userId);
        return defaultPreferences;
      }
    } catch (error) {
      console.error('Erreur lors de la récupération des préférences:', error);
      return defaultPreferences;
    }
  }

  // Créer les préférences par défaut pour un nouvel utilisateur
  static async createDefaultPreferences(userId: string): Promise<void> {
    try {
      const docRef = doc(db, this.COLLECTION_NAME, userId);
      await setDoc(docRef, {
        ...defaultPreferences,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      });
    } catch (error) {
      console.error('Erreur lors de la création des préférences par défaut:', error);
      throw error;
    }
  }

  // Mettre à jour les préférences utilisateur
  static async updateUserPreferences(
    userId: string, 
    preferences: Partial<UserPreferences>
  ): Promise<void> {
    try {
      const docRef = doc(db, this.COLLECTION_NAME, userId);
      await updateDoc(docRef, {
        ...preferences,
        updatedAt: serverTimestamp()
      });
    } catch (error) {
      console.error('Erreur lors de la mise à jour des préférences:', error);
      throw error;
    }
  }

  // Mettre à jour une préférence spécifique
  static async updateSpecificPreference(
    userId: string,
    key: keyof UserPreferences,
    value: any
  ): Promise<void> {
    try {
      const docRef = doc(db, this.COLLECTION_NAME, userId);
      await updateDoc(docRef, {
        [key]: value,
        updatedAt: serverTimestamp()
      });
    } catch (error) {
      console.error(`Erreur lors de la mise à jour de la préférence ${key}:`, error);
      throw error;
    }
  }

  // Mettre à jour les préférences binaurales
  static async updateBinauralPreferences(
    userId: string,
    binauralPrefs: Partial<UserPreferences['binaural']>
  ): Promise<void> {
    try {
      const currentPrefs = await this.getUserPreferences(userId);
      const updatedBinaural = {
        ...currentPrefs.binaural,
        ...binauralPrefs
      };

      await this.updateSpecificPreference(userId, 'binaural', updatedBinaural);
    } catch (error) {
      console.error('Erreur lors de la mise à jour des préférences binaurales:', error);
      throw error;
    }
  }

  // Mettre à jour les préférences de session
  static async updateSessionPreferences(
    userId: string,
    sessionPrefs: Partial<UserPreferences['sessions']>
  ): Promise<void> {
    try {
      const currentPrefs = await this.getUserPreferences(userId);
      const updatedSessions = {
        ...currentPrefs.sessions,
        ...sessionPrefs
      };

      await this.updateSpecificPreference(userId, 'sessions', updatedSessions);
    } catch (error) {
      console.error('Erreur lors de la mise à jour des préférences de session:', error);
      throw error;
    }
  }

  // Mettre à jour les préférences d'accessibilité
  static async updateAccessibilityPreferences(
    userId: string,
    accessibilityPrefs: Partial<UserPreferences['accessibility']>
  ): Promise<void> {
    try {
      const currentPrefs = await this.getUserPreferences(userId);
      const updatedAccessibility = {
        ...currentPrefs.accessibility,
        ...accessibilityPrefs
      };

      await this.updateSpecificPreference(userId, 'accessibility', updatedAccessibility);
    } catch (error) {
      console.error('Erreur lors de la mise à jour des préférences d\'accessibilité:', error);
      throw error;
    }
  }

  // Réinitialiser les préférences aux valeurs par défaut
  static async resetToDefaults(userId: string): Promise<UserPreferences> {
    try {
      const docRef = doc(db, this.COLLECTION_NAME, userId);
      await updateDoc(docRef, {
        ...defaultPreferences,
        updatedAt: serverTimestamp()
      });
      return defaultPreferences;
    } catch (error) {
      console.error('Erreur lors de la réinitialisation des préférences:', error);
      throw error;
    }
  }

  // Exporter les préférences utilisateur (pour sauvegarde/migration)
  static async exportUserPreferences(userId: string): Promise<UserPreferences> {
    return await this.getUserPreferences(userId);
  }

  // Importer les préférences utilisateur (pour restauration/migration)
  static async importUserPreferences(
    userId: string, 
    preferences: UserPreferences
  ): Promise<void> {
    try {
      const docRef = doc(db, this.COLLECTION_NAME, userId);
      await setDoc(docRef, {
        ...preferences,
        updatedAt: serverTimestamp()
      }, { merge: true });
    } catch (error) {
      console.error('Erreur lors de l\'importation des préférences:', error);
      throw error;
    }
  }

  // Vérifier si l'utilisateur a des préférences personnalisées
  static async hasCustomPreferences(userId: string): Promise<boolean> {
    try {
      const docRef = doc(db, this.COLLECTION_NAME, userId);
      const docSnap = await getDoc(docRef);
      return docSnap.exists();
    } catch (error) {
      console.error('Erreur lors de la vérification des préférences:', error);
      return false;
    }
  }
}

// Hook personnalisé pour utiliser les préférences utilisateur (optionnel)
export const useUserPreferences = (userId: string | undefined) => {
  const [preferences, setPreferences] = React.useState<UserPreferences>(defaultPreferences);
  const [loading, setLoading] = React.useState(true);

  React.useEffect(() => {
    if (userId) {
      UserPreferencesService.getUserPreferences(userId)
        .then(prefs => {
          setPreferences(prefs);
          setLoading(false);
        })
        .catch(error => {
          console.error('Erreur lors du chargement des préférences:', error);
          setPreferences(defaultPreferences);
          setLoading(false);
        });
    } else {
      setPreferences(defaultPreferences);
      setLoading(false);
    }
  }, [userId]);

  const updatePreferences = async (newPrefs: Partial<UserPreferences>) => {
    if (userId) {
      try {
        await UserPreferencesService.updateUserPreferences(userId, newPrefs);
        setPreferences(prev => ({ ...prev, ...newPrefs }));
      } catch (error) {
        console.error('Erreur lors de la mise à jour des préférences:', error);
        throw error;
      }
    }
  };

  return {
    preferences,
    loading,
    updatePreferences
  };
};
