import { TypedUseSelectorHook, useDispatch, useSelector } from 'react-redux';
import { createSelector } from '@reduxjs/toolkit';
import type { RootState, AppDispatch } from './index';

// Hooks typés pour Redux
export const useAppDispatch = () => useDispatch<AppDispatch>();
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;

// Sélecteurs de base
const selectAuthState = (state: RootState) => state.auth;
const selectSessionsState = (state: RootState) => state.sessions;
const selectJournalState = (state: RootState) => state.journal;
const selectAudioAssetsState = (state: RootState) => state.audioAssets;
const selectSyncState = (state: RootState) => state.sync;
const selectNetworkState = (state: RootState) => state.network;
const selectUserPreferencesState = (state: RootState) => state.userPreferences;

// Sélecteurs mémorisés avec createSelector pour optimiser les performances
const selectAuthData = createSelector(
  [selectAuthState],
  (auth) => ({
    user: auth.user,
    isAuthenticated: auth.isAuthenticated,
    isLoading: auth.isLoading,
    error: auth.error,
  })
);

const selectSessionsData = createSelector(
  [selectSessionsState],
  (sessions) => ({
    sessions: sessions.sessions,
    sessionsByLanguage: sessions.sessionsByLanguage,
    currentSession: sessions.currentSession,
    isLoading: sessions.isLoading,
    error: sessions.error,
    lastFetchTimestamp: sessions.lastFetchTimestamp,
  })
);

const selectJournalData = createSelector(
  [selectJournalState],
  (journal) => ({
    entries: journal.entries,
    isLoading: journal.isLoading,
    error: journal.error,
  })
);

const selectAudioAssetsData = createSelector(
  [selectAudioAssetsState],
  (audioAssets) => ({
    assets: audioAssets.assets,
    isLoading: audioAssets.isLoading,
    error: audioAssets.error,
  })
);

const selectSyncData = createSelector(
  [selectSyncState],
  (sync) => ({
    isSyncing: sync.isSyncing,
    syncStatus: sync.syncStatus,
    syncErrors: sync.syncErrors,
    lastSyncTimestamp: sync.lastSyncTimestamp,
  })
);

const selectNetworkData = createSelector(
  [selectNetworkState],
  (network) => ({
    isOnline: network.isOnline,
    connectionType: network.connectionType,
    isSlowConnection: network.isSlowConnection,
    lastOnlineTimestamp: network.lastOnlineTimestamp,
    offlineDuration: network.offlineDuration,
  })
);

const selectUserPreferencesData = createSelector(
  [selectUserPreferencesState],
  (userPreferences) => ({
    preferences: userPreferences.preferences,
    isLoading: userPreferences.isLoading,
    error: userPreferences.error,
    hasUnsavedChanges: userPreferences.hasUnsavedChanges,
    lastSyncedAt: userPreferences.lastSyncedAt,
  })
);

// Sélecteurs composés pour des cas d'usage spécifiques
const selectOfflineStatus = createSelector(
  [selectNetworkData, selectSyncData],
  (network, sync) => ({
    isOffline: !network.isOnline,
    hasPendingChanges: sync.isSyncing,
    syncStatus: sync.syncStatus,
    offlineDuration: network.offlineDuration,
  })
);

// Hooks personnalisés utilisant les sélecteurs mémorisés
export const useAuth = () => {
  return useAppSelector(selectAuthData);
};

export const useSessions = () => {
  return useAppSelector(selectSessionsData);
};

export const useJournal = () => {
  return useAppSelector(selectJournalData);
};

export const useAudioAssets = () => {
  return useAppSelector(selectAudioAssetsData);
};

export const useSync = () => {
  return useAppSelector(selectSyncData);
};

export const useNetwork = () => {
  return useAppSelector(selectNetworkData);
};

export const useReduxUserPreferences = () => {
  return useAppSelector(selectUserPreferencesData);
};

// Hooks composés pour des cas d'usage spécifiques
export const useOfflineStatus = () => {
  return useAppSelector(selectOfflineStatus);
};
