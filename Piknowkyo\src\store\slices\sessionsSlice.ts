import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { Session } from '../../models';
import { scriptSyncService } from '../../services/scriptSyncService';
import { Language } from '../../LangProvider';

export interface SessionsState {
  sessions: Session[];
  currentSession: Session | null;
  isLoading: boolean;
  error: string | null;
  lastSyncTimestamp: number | null;
  isOffline: boolean;
  syncStatus: 'idle' | 'syncing' | 'success' | 'error';
  pendingChanges: {
    created: Session[];
    updated: Session[];
    deleted: string[];
  };
  // Cache par langue pour offline-first
  sessionsByLanguage: {
    [key in Language]: Session[];
  };
  lastSyncByLanguage: {
    [key in Language]: number | null;
  };
}

const initialState: SessionsState = {
  sessions: [],
  currentSession: null,
  isLoading: false,
  error: null,
  lastSyncTimestamp: null,
  isOffline: !navigator.onLine,
  syncStatus: 'idle',
  pendingChanges: {
    created: [],
    updated: [],
    deleted: [],
  },
  sessionsByLanguage: {
    fr: [],
    en: [],
    es: [],
  },
  lastSyncByLanguage: {
    fr: null,
    en: null,
    es: null,
  },
};

// Actions asynchrones pour approche offline-first
export const fetchSessionsByLanguage = createAsyncThunk(
  'sessions/fetchSessionsByLanguage',
  async (language: Language, { rejectWithValue, getState }) => {
    try {
      const state = getState() as { sessions: SessionsState };

      // 1. D'abord, essayer de charger depuis le cache local (Redux)
      const cachedSessions = state.sessions.sessionsByLanguage[language];
      const lastSync = state.sessions.lastSyncByLanguage[language];
      const cacheAge = lastSync ? Date.now() - lastSync : Infinity;

      // Si le cache est récent (moins de 5 minutes) et qu'on a des données, les utiliser
      if (cachedSessions.length > 0 && cacheAge < 5 * 60 * 1000) {
        console.log(`📱 Utilisation du cache Redux pour ${language}`);
        return { sessions: cachedSessions, source: 'cache', language };
      }

      // 2. Essayer Firestore si en ligne
      if (navigator.onLine) {
        try {
          console.log(`🔥 Tentative de chargement depuis Firestore pour ${language}`);
          const firestoreSessions = await scriptSyncService.getScriptsFromFirestore(language);

          if (firestoreSessions.length > 0) {
            console.log(`✅ ${firestoreSessions.length} sessions chargées depuis Firestore pour ${language}`);
            return { sessions: firestoreSessions, source: 'firestore', language };
          }
        } catch (error) {
          console.warn(`⚠️ Erreur Firestore pour ${language}, fallback vers local:`, error);
        }
      }

      // 3. Fallback vers les fichiers locaux
      console.log(`📁 Fallback vers les fichiers locaux pour ${language}`);
      const localSessions = await loadLocalSessions(language);
      return { sessions: localSessions, source: 'local', language };

    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

// Fonction helper pour charger les sessions locales
async function loadLocalSessions(language: Language): Promise<Session[]> {
  try {
    // Charger le manifeste local
    const manifestResponse = await fetch(`/assets/manifests/manifest_${language}.json`);
    if (!manifestResponse.ok) {
      throw new Error(`Manifeste local non trouvé pour ${language}`);
    }

    const manifestData = await manifestResponse.json();
    const sessions: Session[] = [];

    // Charger chaque script individuellement
    for (const sessionEntry of manifestData.sessions || []) {
      try {
        const scriptResponse = await fetch(`/assets/sessionScripts/${language}/${sessionEntry.id}.json`);
        if (scriptResponse.ok) {
          const scriptData = await scriptResponse.json();
          sessions.push(scriptData);
        }
      } catch (error) {
        console.warn(`Script local non trouvé: ${sessionEntry.id} (${language})`);
      }
    }

    console.log(`📁 ${sessions.length} sessions chargées localement pour ${language}`);
    return sessions;
  } catch (error) {
    console.error(`Erreur lors du chargement local pour ${language}:`, error);
    return [];
  }
}

// Action pour synchroniser en arrière-plan
export const syncSessionsInBackground = createAsyncThunk(
  'sessions/syncSessionsInBackground',
  async (language: Language, { rejectWithValue }) => {
    try {
      if (!navigator.onLine) {
        throw new Error('Hors ligne - synchronisation impossible');
      }

      console.log(`🔄 Synchronisation en arrière-plan pour ${language}`);
      const firestoreSessions = await scriptSyncService.getScriptsFromFirestore(language);

      return { sessions: firestoreSessions, language };
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

// Action pour forcer le rechargement depuis Firestore
export const forceRefreshSessions = createAsyncThunk(
  'sessions/forceRefreshSessions',
  async (language: Language, { rejectWithValue }) => {
    try {
      console.log(`🔄 Rechargement forcé depuis Firestore pour ${language}`);
      const firestoreSessions = await scriptSyncService.getScriptsFromFirestore(language);

      return { sessions: firestoreSessions, language };
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

export const createSession = createAsyncThunk(
  'sessions/createSession',
  async (sessionData: Omit<Session, 'id'>, { rejectWithValue }) => {
    try {
      const newSession: Session = {
        ...sessionData,
        id: `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      };

      return newSession;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

const sessionsSlice = createSlice({
  name: 'sessions',
  initialState,
  reducers: {
    setCurrentSession: (state, action: PayloadAction<Session | null>) => {
      state.currentSession = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
    updateLastSyncTimestamp: (state, action: PayloadAction<number>) => {
      state.lastSyncTimestamp = action.payload;
    },
    clearPendingChanges: (state) => {
      state.pendingChanges = {
        created: [],
        updated: [],
        deleted: [],
      };
    },
    setOfflineStatus: (state, action: PayloadAction<boolean>) => {
      state.isOffline = action.payload;
    },
    setSyncStatus: (state, action: PayloadAction<'idle' | 'syncing' | 'success' | 'error'>) => {
      state.syncStatus = action.payload;
    },
    // Mettre à jour les sessions pour une langue spécifique
    updateSessionsForLanguage: (state, action: PayloadAction<{ language: Language; sessions: Session[] }>) => {
      const { language, sessions } = action.payload;
      state.sessionsByLanguage[language] = sessions;
      state.lastSyncByLanguage[language] = Date.now();

      // Mettre à jour la liste principale si c'est la langue courante
      // (on peut déterminer la langue courante depuis le contexte ou les props)
      state.sessions = sessions;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch Sessions by Language (offline-first)
      .addCase(fetchSessionsByLanguage.pending, (state) => {
        state.isLoading = true;
        state.error = null;
        state.syncStatus = 'syncing';
      })
      .addCase(fetchSessionsByLanguage.fulfilled, (state, action) => {
        state.isLoading = false;
        state.syncStatus = 'success';

        const { sessions, source, language } = action.payload;

        // Mettre à jour le cache pour cette langue
        state.sessionsByLanguage[language] = sessions;
        state.lastSyncByLanguage[language] = Date.now();

        // Mettre à jour la liste principale
        state.sessions = sessions;
        state.lastSyncTimestamp = Date.now();

        console.log(`✅ Sessions chargées depuis ${source} pour ${language}: ${sessions.length} scripts`);
      })
      .addCase(fetchSessionsByLanguage.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
        state.syncStatus = 'error';
      })

      // Synchronisation en arrière-plan
      .addCase(syncSessionsInBackground.fulfilled, (state, action) => {
        const { sessions, language } = action.payload;

        // Mettre à jour silencieusement le cache
        state.sessionsByLanguage[language] = sessions;
        state.lastSyncByLanguage[language] = Date.now();

        // Si c'est la langue courante, mettre à jour aussi la liste principale
        if (state.sessions.length > 0 && state.sessions[0]?.language === language) {
          state.sessions = sessions;
        }

        console.log(`🔄 Synchronisation en arrière-plan terminée pour ${language}: ${sessions.length} scripts`);
      })

      // Rechargement forcé
      .addCase(forceRefreshSessions.pending, (state) => {
        state.isLoading = true;
        state.syncStatus = 'syncing';
      })
      .addCase(forceRefreshSessions.fulfilled, (state, action) => {
        state.isLoading = false;
        state.syncStatus = 'success';

        const { sessions, language } = action.payload;
        state.sessionsByLanguage[language] = sessions;
        state.lastSyncByLanguage[language] = Date.now();
        state.sessions = sessions;
        state.lastSyncTimestamp = Date.now();
      })
      .addCase(forceRefreshSessions.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
        state.syncStatus = 'error';
      })
      // Create Session
      .addCase(createSession.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createSession.fulfilled, (state, action) => {
        state.isLoading = false;
        state.sessions.push(action.payload);
        
        // Si offline, ajouter aux changements en attente
        if (!navigator.onLine) {
          state.pendingChanges.created.push(action.payload);
        }
      })
      .addCase(createSession.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

export const {
  setCurrentSession,
  clearError,
  updateLastSyncTimestamp,
  clearPendingChanges,
  setOfflineStatus,
  setSyncStatus,
  updateSessionsForLanguage,
} = sessionsSlice.actions;

export default sessionsSlice.reducer;
