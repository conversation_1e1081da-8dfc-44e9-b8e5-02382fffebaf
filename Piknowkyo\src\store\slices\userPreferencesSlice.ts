import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { UserPreferences, UserPreferencesService, defaultPreferences } from '../../services/userPreferencesService';
import { Language } from '../../LangProvider';

interface UserPreferencesState {
  preferences: UserPreferences;
  isLoading: boolean;
  error: string | null;
  lastSyncedAt: number | null;
  hasUnsavedChanges: boolean;
}

const initialState: UserPreferencesState = {
  preferences: defaultPreferences,
  isLoading: false,
  error: null,
  lastSyncedAt: null,
  hasUnsavedChanges: false,
};

// Actions asynchrones
export const loadUserPreferences = createAsyncThunk(
  'userPreferences/load',
  async (userId: string, { rejectWithValue }) => {
    try {
      const preferences = await UserPreferencesService.getUserPreferences(userId);
      return preferences;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Erreur lors du chargement des préférences');
    }
  }
);

export const saveUserPreferences = createAsyncThunk(
  'userPreferences/save',
  async ({ userId, preferences }: { userId: string; preferences: Partial<UserPreferences> }, { rejectWithValue }) => {
    try {
      await UserPreferencesService.updateUserPreferences(userId, preferences);
      return preferences;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Erreur lors de la sauvegarde des préférences');
    }
  }
);

export const updateLanguagePreference = createAsyncThunk(
  'userPreferences/updateLanguage',
  async ({ userId, language }: { userId: string; language: Language }, { rejectWithValue }) => {
    try {
      await UserPreferencesService.updateSpecificPreference(userId, 'language', language);
      return language;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Erreur lors de la mise à jour de la langue');
    }
  }
);

export const updateThemePreference = createAsyncThunk(
  'userPreferences/updateTheme',
  async ({ userId, theme }: { userId: string; theme: 'light' | 'dark' }, { rejectWithValue }) => {
    try {
      await UserPreferencesService.updateSpecificPreference(userId, 'theme', theme);
      return theme;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Erreur lors de la mise à jour du thème');
    }
  }
);

export const resetUserPreferences = createAsyncThunk(
  'userPreferences/reset',
  async (userId: string, { rejectWithValue }) => {
    try {
      await UserPreferencesService.resetToDefaults(userId);
      return defaultPreferences;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Erreur lors de la réinitialisation');
    }
  }
);

const userPreferencesSlice = createSlice({
  name: 'userPreferences',
  initialState,
  reducers: {
    // Actions synchrones pour les modifications locales
    updateLocalPreference: (state, action: PayloadAction<{ key: keyof UserPreferences; value: any }>) => {
      const { key, value } = action.payload;
      (state.preferences as any)[key] = value;
      state.hasUnsavedChanges = true;
    },
    
    updateLocalNestedPreference: (state, action: PayloadAction<{ 
      section: keyof UserPreferences; 
      key: string; 
      value: any 
    }>) => {
      const { section, key, value } = action.payload;
      if (typeof state.preferences[section] === 'object' && state.preferences[section] !== null) {
        (state.preferences[section] as any)[key] = value;
        state.hasUnsavedChanges = true;
      }
    },

    clearError: (state) => {
      state.error = null;
    },

    markAsSaved: (state) => {
      state.hasUnsavedChanges = false;
      state.lastSyncedAt = Date.now();
    },

    // Action pour synchroniser les préférences avec l'état global de l'app
    syncWithAppState: (state, action: PayloadAction<{ language?: Language; theme?: 'light' | 'dark' }>) => {
      const { language, theme } = action.payload;
      if (language && language !== state.preferences.language) {
        state.preferences.language = language;
        state.hasUnsavedChanges = true;
      }
      if (theme && theme !== state.preferences.theme) {
        state.preferences.theme = theme;
        state.hasUnsavedChanges = true;
      }
    },
  },
  extraReducers: (builder) => {
    // Load preferences
    builder
      .addCase(loadUserPreferences.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(loadUserPreferences.fulfilled, (state, action) => {
        state.isLoading = false;
        state.preferences = action.payload;
        state.hasUnsavedChanges = false;
        state.lastSyncedAt = Date.now();
      })
      .addCase(loadUserPreferences.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Save preferences
    builder
      .addCase(saveUserPreferences.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(saveUserPreferences.fulfilled, (state, action) => {
        state.isLoading = false;
        state.preferences = { ...state.preferences, ...action.payload };
        state.hasUnsavedChanges = false;
        state.lastSyncedAt = Date.now();
      })
      .addCase(saveUserPreferences.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Update language
    builder
      .addCase(updateLanguagePreference.fulfilled, (state, action) => {
        state.preferences.language = action.payload;
        state.hasUnsavedChanges = false;
        state.lastSyncedAt = Date.now();
      })
      .addCase(updateLanguagePreference.rejected, (state, action) => {
        state.error = action.payload as string;
      });

    // Update theme
    builder
      .addCase(updateThemePreference.fulfilled, (state, action) => {
        state.preferences.theme = action.payload;
        state.hasUnsavedChanges = false;
        state.lastSyncedAt = Date.now();
      })
      .addCase(updateThemePreference.rejected, (state, action) => {
        state.error = action.payload as string;
      });

    // Reset preferences
    builder
      .addCase(resetUserPreferences.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(resetUserPreferences.fulfilled, (state, action) => {
        state.isLoading = false;
        state.preferences = action.payload;
        state.hasUnsavedChanges = false;
        state.lastSyncedAt = Date.now();
      })
      .addCase(resetUserPreferences.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

export const {
  updateLocalPreference,
  updateLocalNestedPreference,
  clearError,
  markAsSaved,
  syncWithAppState,
} = userPreferencesSlice.actions;

export default userPreferencesSlice.reducer;
