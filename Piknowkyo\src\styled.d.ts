// src/styled.d.ts (ou src/types/styled.d.ts, ou le nom que vous utilisez)

import 'styled-components';

declare module 'styled-components' {
  export interface DefaultTheme {
    // --- Propriétés existantes (vérifiez qu'elles correspondent à vos thèmes) ---
    name?: string; // Utile pour identifier le thème (ex: 'light', 'dark')

    background: string;    // Couleur de fond principale de la page
    surface: string;       // Couleur de fond pour les éléments "surélevés" comme les cartes, modales
    surfaceAlt: string;    // Couleur de fond alternative, légèrement différente de surface

    primary: string;       // Couleur principale pour les actions, liens importants, titres
    primaryDark: string;   // Version plus foncée de la couleur principale
    secondary: string;     // Couleur secondaire, souvent pour les boutons de second plan, accents
    secondaryDark: string; // Version plus foncée de la couleur secondaire
    accent: string;        // Couleur d'accentuation, pour attirer l'attention
    success: string;       // Couleur pour les succès et validations

    text: string;          // Couleur de texte principale sur `background`
    textSecondary: string; // Couleur de texte secondaire, moins proéminente
    textLight: string;     // Texte clair, typiquement sur `primary`, `accent` ou fonds sombres
    textMuted?: string;     // Texte très discret, pour les informations de second plan (optionnel)
    textLightOnPrimary?: string; // Couleur de texte spécifique à utiliser sur `primary` si `textLight` ne convient pas (optionnel)

    border: string;        // Couleur pour les bordures
    
    cardShadow: string;    // Ombre par défaut pour les cartes
    headerShadow: string;  // Ombre pour l'en-tête de page

    // Propriétés spécifiques à votre design que vous aviez déjà :
    logoBg?: string;        // Peut-être pour un fond spécifique au logo (optionnel)
    navActive?: string;     // Couleur pour un lien de navigation actif (optionnel)
    navInactive?: string;   // Couleur pour un lien de navigation inactif (optionnel)
    
    gradientStart?: string; // Début d'un dégradé (optionnel)
    gradientEnd?: string;   // Fin d'un dégradé (optionnel)
    primaryGradient?: string; // Un dégradé utilisant la couleur primaire (ex: `linear-gradient(...)`) (optionnel)


    // --- Propriétés pour les formulaires et états ---
    inputBackground?: string; // Fond des champs de formulaire (optionnel, peut utiliser `surfaceAlt`)
    errorColor?: string;      // Couleur pour les messages d'erreur, bordures d'erreur (optionnel)
    
    disabled?: string;        // ANCIENNE propriété que vous aviez, peut-être pour un style 'disabled' générique.
                               // Considérez d'utiliser disabledBackground et disabledText pour plus de contrôle.
    
    // === NOUVELLES PROPRIÉTÉS BASÉES SUR LES ERREURS ET STYLES ===
    disabledBackground?: string; // Fond pour les éléments désactivés (boutons, inputs)
    disabledText?: string;       // Couleur du texte pour les éléments désactivés

    cardHoverShadow?: string;    // Ombre pour les cartes au survol (légèrement différente de cardShadow)
    
    heroOverlay?: string;        // Couleur (avec opacité) pour la superposition sur les images Hero (optionnel)

    borderSlight: string;
    shadowSmall: string;

    // --- Propriétés supplémentaires utiles (basées sur vos GlobalStyles) ---
    // Si vous voulez des couleurs spécifiques pour le hover des boutons, etc.
    // buttonPrimaryHoverBackground?: string;
    // buttonPrimaryHoverText?: string;
    
    // Pour la scrollbar (optionnel, vous pouvez aussi utiliser des valeurs fixes ou dérivées dans GlobalStyles)
    // scrollbarTrackColor?: string;
    // scrollbarThumbColor?: string;
    // scrollbarThumbHoverColor?: string;
    
    zenTetrisPiece1?: string;
    zenTetrisPiece2?: string;
    zenTetrisPiece3?: string;
    zenTetrisPiece4?: string;
    zenTetrisPiece5?: string;
    zenTetrisPiece6?: string;
    zenTetrisPiece7?: string;
    zenTetrisBackgroundCell?: string;
    zenTetrisBoardBackground?: string;
    hoverShadow?: string; // Ajouté dans GameCard hover
  }
}