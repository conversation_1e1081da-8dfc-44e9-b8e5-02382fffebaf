{"root": ["./src/app.tsx", "./src/globalstyles.tsx", "./src/langprovider.tsx", "./src/themeprovider.tsx", "./src/firebase.ts", "./src/i18n.ts", "./src/index.tsx", "./src/models.ts", "./src/react-app-env.d.ts", "./src/reportwebvitals.ts", "./src/setuptests.ts", "./src/styled.d.ts", "./src/themes.ts", "./src/vite-env.d.ts", "./src/components/audioconfigpanel.tsx", "./src/components/authmodal.tsx", "./src/components/authtest.tsx", "./src/components/bottombar.tsx", "./src/components/journalentryform.tsx", "./src/components/languageswitcher.tsx", "./src/components/lockedsessioncard.tsx", "./src/components/mainmenu.tsx", "./src/components/monetizationmodal.tsx", "./src/components/networkstatusnotifier.tsx", "./src/components/notificationtest.tsx", "./src/components/preferences.tsx", "./src/components/questionnaire.tsx", "./src/components/reduxexample.tsx", "./src/components/reusablemodal.tsx", "./src/components/sessionslist.tsx", "./src/components/splashscreen.tsx", "./src/components/subscriptionstatus.tsx", "./src/components/syncstatusindicator.tsx", "./src/components/toast.tsx", "./src/components/unlocktimer.tsx", "./src/components/userprofile.tsx", "./src/components/common/button.tsx", "./src/components/common/card.tsx", "./src/components/common/index.ts", "./src/components/ui/button.tsx", "./src/components/ui/card.tsx", "./src/components/ui/index.ts", "./src/contexts/authcontext.tsx", "./src/data/audioassets.ts", "./src/data/sessions.ts", "./src/games/common/gameutils.ts", "./src/games/common/models.tsx", "./src/games/common/components/gamemodal.tsx", "./src/games/common/components/gametimer.tsx", "./src/games/common/components/scoredisplay.tsx", "./src/games/zen-tetris/gamecomponent.tsx", "./src/games/zen-tetris/logic.ts", "./src/games/zen-tetris/styles.ts", "./src/games/zen-tetris/components/orientationhint.tsx", "./src/games/zen-tetris/hooks/useorientation.ts", "./src/games/zen-tetris/utils/haptics.ts", "./src/hooks/useaccesscontrol.ts", "./src/models/script.d.ts", "./src/models/script.model.ts", "./src/pages/aboutpage.tsx", "./src/pages/audioassetsconfigpage.tsx", "./src/pages/blogpage.tsx", "./src/pages/blogpostcommentspage.tsx", "./src/pages/categoriespage.tsx", "./src/pages/gamespage.tsx", "./src/pages/historypage.tsx", "./src/pages/homepage.tsx", "./src/pages/journalpage.tsx", "./src/pages/leaderboardpage.tsx", "./src/pages/monetizationpage.tsx", "./src/pages/notfoundpage.tsx", "./src/pages/playerpage.tsx", "./src/pages/profilepage.tsx", "./src/pages/quizpage.tsx", "./src/pages/sessiondetailpage.tsx", "./src/pages/sessionspage.tsx", "./src/pages/settingspage.tsx", "./src/pages/statspage.tsx", "./src/pages/userprofilepage.tsx", "./src/services/accesscontrolservice.ts", "./src/services/blogservice.ts", "./src/services/firebase.ts", "./src/services/monetizationservice.ts", "./src/services/scriptsyncservice.ts", "./src/services/scriptsservice.ts", "./src/services/syncservice.ts", "./src/services/tts.ts", "./src/services/ttsvoices.ts", "./src/services/usenetworkstatus.ts", "./src/services/usepushnotifications.ts", "./src/services/userpreferencesservice.ts", "./src/store/hooks.ts", "./src/store/index.ts", "./src/store/slices/audioassetsslice.ts", "./src/store/slices/authslice.ts", "./src/store/slices/journalslice.ts", "./src/store/slices/monetizationslice.ts", "./src/store/slices/networkslice.ts", "./src/store/slices/sessionsslice.ts", "./src/store/slices/syncslice.ts", "./src/testjoff/testfirebase.tsx"], "errors": true, "version": "5.8.3"}