# Gestion des Scripts PiKnowKyo - Interface d'Administration

Cette interface d'administration permet de gérer les scripts PiKnowKyo directement depuis le web, avec synchronisation automatique vers Firestore.

## 🚀 Fonctionnalités

### ✅ **Gestion complète des scripts**
- **C<PERSON>er** de nouveaux scripts
- **Modifier** les scripts existants
- **Supprimer** des scripts
- **Prévisualiser** les scripts avec lecteur intégré
- **Synchroniser** avec Firestore en temps réel

### 📝 **Éditeur avancé**
- Interface à onglets (Métadonnées / Script)
- Éditeur de segments avec contrôles TTS
- Gestion des tags avec suggestions
- Catégorisation automatique
- Validation des données

### 🔍 **Interface de gestion**
- Filtrage par langue (FR/EN/ES)
- Recherche par titre/description
- Filtrage par type de script
- Vue en cartes avec informations clés
- Statistiques en temps réel

### 🎵 **Lecteur intégré**
- Prévisualisation des scripts
- Simulation de lecture
- Affichage des paramètres TTS
- Navigation par segments

## 🛠️ Installation et Configuration

### 1. Démarrer l'interface d'administration

```bash
cd Pmanager
npm run dev
```

L'interface sera accessible sur : http://localhost:3001

### 2. Connexion

Utilisez vos identifiants d'administrateur pour accéder à l'interface.

### 3. Accéder à la gestion des scripts

Dans le menu latéral, cliquez sur **"Scripts"** pour accéder à l'interface de gestion.

## 📋 Guide d'utilisation

### **Créer un nouveau script**

1. Cliquez sur **"Nouveau Script"**
2. **Onglet Métadonnées** :
   - Titre du script
   - Type (Hypnose, Méditation, etc.)
   - Durée estimée
   - Description et bénéfices
   - Tags (avec suggestions automatiques)
   - Configuration Premium/Gratuit

3. **Onglet Script** :
   - Ajoutez des segments de texte
   - Configurez les pauses (en millisecondes)
   - Ajustez le pitch (hauteur de voix)
   - Définissez la vitesse de lecture

4. **Sauvegarde** : Le script est automatiquement synchronisé avec Firestore

### **Modifier un script existant**

1. Trouvez le script dans la liste
2. Cliquez sur l'icône **"Modifier"** (crayon)
3. Effectuez vos modifications
4. Sauvegardez → La version est automatiquement incrémentée

### **Prévisualiser un script**

1. Cliquez sur l'icône **"Prévisualiser"** (œil)
2. Utilisez le lecteur intégré pour simuler la lecture
3. Naviguez entre les segments
4. Visualisez tous les paramètres TTS

### **Supprimer un script**

1. Cliquez sur l'icône **"Supprimer"** (poubelle)
2. Confirmez la suppression
3. Le script est supprimé de Firestore et des manifestes

## 🔄 Synchronisation automatique

### **Vers Firestore**
- Chaque modification est automatiquement synchronisée
- Les manifestes sont mis à jour en temps réel
- Les utilisateurs reçoivent les mises à jour instantanément

### **Versioning**
- Chaque modification incrémente la version
- Historique des modifications conservé
- Statistiques préservées (vues, notes)

## 📊 Structure des données

### **Script Firestore**
```json
{
  "id": "mon_script",
  "title": "Mon Super Script",
  "description": "Description du script",
  "type": "meditation",
  "language": "fr",
  "durationMinutes": 30,
  "category": "développement-personnel",
  "tags": ["relaxation", "confiance"],
  "isPremium": false,
  "version": 2,
  "script": [
    {
      "text": "Fermez les yeux et respirez profondément...",
      "pause": 2000,
      "pitch": 1.0,
      "rate": 0.8
    }
  ],
  "playCount": 0,
  "rating": 0,
  "updatedAt": "2025-01-15T10:30:00.000Z"
}
```

### **Catégories automatiques**
Les scripts sont automatiquement catégorisés selon leurs tags :

- **développement-personnel** : estime-de-soi, confiance-en-soi, motivation
- **santé-bien-être** : relaxation, sommeil, guérison, forme-physique
- **émotionnel** : deuil, acceptation, libération, paix-intérieure
- **addictions-blocages** : addictions, blocages-mentaux, phobies
- **alimentation** : gestion-du-poids, alimentation-saine
- **abondance-finances** : abondance, richesse, prospérité
- **spiritualité** : chakras, énergie, méditation
- **enfants** : enfants, hypnose-enfants, tdah
- **mémoire-concentration** : mémoire, concentration, clarté

## 🎯 Workflow recommandé

### **Pour un nouveau script :**
1. Créer le script dans l'interface web
2. Tester avec la prévisualisation
3. Publier → Synchronisation automatique
4. Les utilisateurs reçoivent le nouveau script

### **Pour modifier un script existant :**
1. Modifier dans l'interface web
2. Prévisualiser les changements
3. Sauvegarder → Version incrémentée automatiquement
4. Les utilisateurs reçoivent la mise à jour

### **Pour gérer plusieurs langues :**
1. Créer le script dans une langue
2. Utiliser la fonction de duplication pour les autres langues
3. Adapter le contenu pour chaque langue
4. Synchroniser toutes les versions

## 🔧 Fonctionnalités avancées

### **Synchronisation manuelle**
- Bouton "Synchroniser avec Firestore" pour forcer la synchronisation
- Utile en cas de problème de réseau

### **Statistiques en temps réel**
- Nombre de scripts par langue
- Répartition par type et catégorie
- Statistiques d'utilisation

### **Gestion des permissions**
- Seuls les utilisateurs avec la permission `manage_sessions` peuvent accéder
- Contrôle d'accès granulaire

## 🚨 Points importants

### **Sauvegarde automatique**
- Toutes les modifications sont sauvegardées en temps réel
- Pas de perte de données possible

### **Validation des données**
- Titre et description obligatoires
- Validation des paramètres TTS
- Génération automatique des IDs

### **Performance**
- Chargement optimisé par langue
- Cache intelligent
- Interface responsive

## 🆘 Dépannage

### **Erreur de connexion Firestore**
1. Vérifiez la configuration Firebase
2. Vérifiez les règles de sécurité Firestore
3. Vérifiez la connexion internet

### **Script non visible côté utilisateur**
1. Vérifiez que la synchronisation s'est bien passée
2. Vérifiez les manifestes dans Firestore
3. Forcez la synchronisation si nécessaire

### **Erreur de permissions**
1. Vérifiez que l'utilisateur a la permission `manage_sessions`
2. Vérifiez l'authentification Firebase

## 📞 Support

Pour toute question ou problème, consultez les logs de la console ou contactez l'équipe de développement.

---

**Interface développée pour PiKnowKyo - Gestion centralisée des scripts d'hypnose et méditation** 🧘‍♀️✨
