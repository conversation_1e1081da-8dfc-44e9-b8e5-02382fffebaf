import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Switch,
  Chip,
  Box,
  Typography,
  Tabs,
  Tab,
  Alert,
  IconButton,
  Tooltip,
} from '@mui/material';
import { Add, Delete, PlayArrow, Pause } from '@mui/icons-material';
import { Script, ScriptSegment, scriptService } from '@/services/scriptService';

interface ScriptEditorProps {
  open: boolean;
  onClose: () => void;
  script: Script | null;
  language: 'fr' | 'en' | 'es';
  onSave: (script: Script) => void;
}

const SCRIPT_TYPES = [
  { value: 'hypnosis', label: 'Hypnose' },
  { value: 'meditation', label: 'Méditation' },
  { value: 'training', label: 'Formation' },
  { value: 'visualization', label: 'Visualisation' },
  { value: 'story', label: 'Histoire' },
  { value: 'silence', label: 'Silence' },
];

const COMMON_TAGS = [
  'relaxation', 'sommeil', 'stress', 'anxiété', 'confiance-en-soi', 'estime-de-soi',
  'motivation', 'concentration', 'mémoire', 'créativité', 'guérison', 'douleur',
  'habitudes', 'addictions', 'poids', 'alimentation', 'sport', 'performance',
  'relations', 'communication', 'leadership', 'succès', 'abondance', 'argent',
  'spiritualité', 'méditation', 'pleine-conscience', 'chakras', 'énergie',
  'enfants', 'adolescents', 'seniors', 'femmes', 'hommes'
];

const ScriptEditor: React.FC<ScriptEditorProps> = ({
  open,
  onClose,
  script,
  language,
  onSave
}) => {
  const [activeTab, setActiveTab] = useState(0);
  const [formData, setFormData] = useState<Partial<Script>>({
    id: '',
    title: '',
    description: '',
    benefits: '',
    type: 'meditation',
    language: language,
    durationMinutes: 30,
    tags: [],
    isPremium: false,
    price: 0,
    script: []
  });
  const [newTag, setNewTag] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (script) {
      setFormData(script);
    } else {
      setFormData({
        id: '',
        title: '',
        description: '',
        benefits: '',
        type: 'meditation',
        language: language,
        durationMinutes: 30,
        tags: [],
        isPremium: false,
        price: 0,
        script: []
      });
    }
    setActiveTab(0);
    setError(null);
  }, [script, language, open]);

  const handleInputChange = (field: keyof Script, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleAddTag = () => {
    if (newTag.trim() && !formData.tags?.includes(newTag.trim())) {
      handleInputChange('tags', [...(formData.tags || []), newTag.trim()]);
      setNewTag('');
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    handleInputChange('tags', formData.tags?.filter(tag => tag !== tagToRemove) || []);
  };

  const handleAddScriptSegment = () => {
    const newSegment: ScriptSegment = {
      text: '',
      pause: 2000,
      pitch: 1.0,
      rate: 0.8
    };
    handleInputChange('script', [...(formData.script || []), newSegment]);
  };

  const handleUpdateScriptSegment = (index: number, field: keyof ScriptSegment, value: any) => {
    const updatedScript = [...(formData.script || [])];
    updatedScript[index] = {
      ...updatedScript[index],
      [field]: value
    };
    handleInputChange('script', updatedScript);
  };

  const handleRemoveScriptSegment = (index: number) => {
    const updatedScript = formData.script?.filter((_, i) => i !== index) || [];
    handleInputChange('script', updatedScript);
  };

  const generateScriptId = (title: string): string => {
    return title
      .toLowerCase()
      .replace(/[àáâãäå]/g, 'a')
      .replace(/[èéêë]/g, 'e')
      .replace(/[ìíîï]/g, 'i')
      .replace(/[òóôõö]/g, 'o')
      .replace(/[ùúûü]/g, 'u')
      .replace(/[ç]/g, 'c')
      .replace(/[ñ]/g, 'n')
      .replace(/[^a-z0-9\s]/g, '')
      .replace(/\s+/g, '_')
      .substring(0, 50);
  };

  const handleSave = async () => {
    try {
      setLoading(true);
      setError(null);

      // Validation
      if (!formData.title?.trim()) {
        throw new Error('Le titre est requis');
      }

      if (!formData.description?.trim()) {
        throw new Error('La description est requise');
      }

      // Générer l'ID si c'est un nouveau script
      if (!script) {
        formData.id = generateScriptId(formData.title);
      }

      // Calculer la durée estimée basée sur le script
      if (formData.script && formData.script.length > 0) {
        const totalDuration = formData.script.reduce((total, segment) => {
          const textDuration = (segment.text.length / 10) * 1000; // ~10 caractères par seconde
          return total + textDuration + segment.pause;
        }, 0);
        formData.estimatedDuration = Math.round(totalDuration / 60000); // Convertir en minutes
      }

      const savedScript = await scriptService.saveScript(formData, !script);
      onSave(savedScript);
    } catch (error: any) {
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const renderMetadataTab = () => (
    <Grid container spacing={2}>
      <Grid item xs={12}>
        <TextField
          fullWidth
          label="Titre"
          value={formData.title || ''}
          onChange={(e) => handleInputChange('title', e.target.value)}
          required
        />
      </Grid>
      
      <Grid item xs={12} sm={6}>
        <FormControl fullWidth>
          <InputLabel>Type</InputLabel>
          <Select
            value={formData.type || 'meditation'}
            onChange={(e) => handleInputChange('type', e.target.value)}
            label="Type"
          >
            {SCRIPT_TYPES.map(type => (
              <MenuItem key={type.value} value={type.value}>
                {type.label}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Grid>

      <Grid item xs={12} sm={6}>
        <TextField
          fullWidth
          label="Durée (minutes)"
          type="number"
          value={formData.durationMinutes || 30}
          onChange={(e) => handleInputChange('durationMinutes', parseInt(e.target.value))}
        />
      </Grid>

      <Grid item xs={12}>
        <TextField
          fullWidth
          label="Description"
          multiline
          rows={3}
          value={formData.description || ''}
          onChange={(e) => handleInputChange('description', e.target.value)}
          required
        />
      </Grid>

      <Grid item xs={12}>
        <TextField
          fullWidth
          label="Bénéfices"
          multiline
          rows={2}
          value={formData.benefits || ''}
          onChange={(e) => handleInputChange('benefits', e.target.value)}
        />
      </Grid>

      <Grid item xs={12} sm={6}>
        <FormControlLabel
          control={
            <Switch
              checked={formData.isPremium || false}
              onChange={(e) => handleInputChange('isPremium', e.target.checked)}
            />
          }
          label="Script Premium"
        />
      </Grid>

      {formData.isPremium && (
        <Grid item xs={12} sm={6}>
          <TextField
            fullWidth
            label="Prix (€)"
            type="number"
            step="0.01"
            value={formData.price || 0}
            onChange={(e) => handleInputChange('price', parseFloat(e.target.value))}
          />
        </Grid>
      )}

      {/* Tags */}
      <Grid item xs={12}>
        <Typography variant="subtitle2" gutterBottom>
          Tags
        </Typography>
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mb: 1 }}>
          {formData.tags?.map((tag) => (
            <Chip
              key={tag}
              label={tag}
              onDelete={() => handleRemoveTag(tag)}
              size="small"
            />
          ))}
        </Box>
        <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
          <TextField
            size="small"
            label="Nouveau tag"
            value={newTag}
            onChange={(e) => setNewTag(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && handleAddTag()}
          />
          <Button onClick={handleAddTag} disabled={!newTag.trim()}>
            Ajouter
          </Button>
        </Box>
        <Box sx={{ mt: 1 }}>
          <Typography variant="caption" color="text.secondary">
            Tags suggérés:
          </Typography>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mt: 0.5 }}>
            {COMMON_TAGS.filter(tag => !formData.tags?.includes(tag)).slice(0, 10).map((tag) => (
              <Chip
                key={tag}
                label={tag}
                size="small"
                variant="outlined"
                onClick={() => handleInputChange('tags', [...(formData.tags || []), tag])}
                sx={{ cursor: 'pointer' }}
              />
            ))}
          </Box>
        </Box>
      </Grid>
    </Grid>
  );

  const renderScriptTab = () => (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">
          Contenu du Script ({formData.script?.length || 0} segments)
        </Typography>
        <Button
          variant="contained"
          startIcon={<Add />}
          onClick={handleAddScriptSegment}
        >
          Ajouter un segment
        </Button>
      </Box>

      {formData.script?.map((segment, index) => (
        <Box key={index} sx={{ mb: 3, p: 2, border: '1px solid #ddd', borderRadius: 1 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
            <Typography variant="subtitle2">
              Segment {index + 1}
            </Typography>
            <IconButton
              size="small"
              color="error"
              onClick={() => handleRemoveScriptSegment(index)}
            >
              <Delete />
            </IconButton>
          </Box>

          <Grid container spacing={2}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Texte"
                multiline
                rows={3}
                value={segment.text}
                onChange={(e) => handleUpdateScriptSegment(index, 'text', e.target.value)}
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                label="Pause (ms)"
                type="number"
                value={segment.pause}
                onChange={(e) => handleUpdateScriptSegment(index, 'pause', parseInt(e.target.value))}
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                label="Pitch"
                type="number"
                step="0.1"
                value={segment.pitch}
                onChange={(e) => handleUpdateScriptSegment(index, 'pitch', parseFloat(e.target.value))}
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                label="Vitesse"
                type="number"
                step="0.1"
                value={segment.rate}
                onChange={(e) => handleUpdateScriptSegment(index, 'rate', parseFloat(e.target.value))}
              />
            </Grid>
          </Grid>
        </Box>
      ))}

      {(!formData.script || formData.script.length === 0) && (
        <Alert severity="info">
          Aucun segment de script. Cliquez sur "Ajouter un segment" pour commencer.
        </Alert>
      )}
    </Box>
  );

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        {script ? `Modifier: ${script.title}` : 'Nouveau Script'}
      </DialogTitle>
      
      <DialogContent>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        <Tabs value={activeTab} onChange={(_, value) => setActiveTab(value)} sx={{ mb: 2 }}>
          <Tab label="Métadonnées" />
          <Tab label="Script" />
        </Tabs>

        {activeTab === 0 && renderMetadataTab()}
        {activeTab === 1 && renderScriptTab()}
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose} disabled={loading}>
          Annuler
        </Button>
        <Button
          onClick={handleSave}
          variant="contained"
          disabled={loading || !formData.title?.trim()}
        >
          {loading ? 'Sauvegarde...' : 'Sauvegarder'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ScriptEditor;
