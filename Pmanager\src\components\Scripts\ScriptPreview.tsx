import React, { useState } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  <PERSON>alogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  Chip,
  Grid,
  Card,
  CardContent,
  IconButton,
  Tooltip,
  Divider,
  List,
  ListItem,
  ListItemText,
  LinearProgress,
} from '@mui/material';
import {
  PlayArrow,
  Pause,
  Stop,
  VolumeUp,
  Speed,
  Timer,
  Category,
  Star,
  Visibility,
} from '@mui/icons-material';
import { Script } from '@/services/scriptService';

interface ScriptPreviewProps {
  open: boolean;
  onClose: () => void;
  script: Script | null;
}

const ScriptPreview: React.FC<ScriptPreviewProps> = ({ open, onClose, script }) => {
  const [currentSegment, setCurrentSegment] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const [playbackProgress, setPlaybackProgress] = useState(0);

  if (!script) return null;

  const getTypeColor = (type: string) => {
    const colors: Record<string, 'primary' | 'secondary' | 'success' | 'warning' | 'info' | 'error'> = {
      hypnosis: 'primary',
      meditation: 'success',
      training: 'warning',
      visualization: 'info',
      story: 'secondary',
      silence: 'error',
    };
    return colors[type] || 'default';
  };

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return hours > 0 ? `${hours}h ${mins}min` : `${mins}min`;
  };

  const simulatePlayback = () => {
    if (isPlaying) {
      setIsPlaying(false);
      return;
    }

    setIsPlaying(true);
    // Simulation simple de lecture
    const interval = setInterval(() => {
      setPlaybackProgress(prev => {
        if (prev >= 100) {
          setIsPlaying(false);
          clearInterval(interval);
          return 0;
        }
        return prev + 2;
      });
    }, 100);
  };

  const stopPlayback = () => {
    setIsPlaying(false);
    setPlaybackProgress(0);
    setCurrentSegment(0);
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="lg" fullWidth>
      <DialogTitle>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h5">
            Prévisualisation: {script.title}
          </Typography>
          <Chip
            label={script.type}
            color={getTypeColor(script.type)}
            size="small"
          />
        </Box>
      </DialogTitle>

      <DialogContent>
        <Grid container spacing={3}>
          {/* Informations générales */}
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Informations
                </Typography>
                
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" color="text.secondary">
                    Description
                  </Typography>
                  <Typography variant="body1">
                    {script.description}
                  </Typography>
                </Box>

                {script.benefits && (
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" color="text.secondary">
                      Bénéfices
                    </Typography>
                    <Typography variant="body1">
                      {script.benefits}
                    </Typography>
                  </Box>
                )}

                <Divider sx={{ my: 2 }} />

                <Grid container spacing={1}>
                  <Grid item xs={6}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Timer fontSize="small" />
                      <Typography variant="body2">
                        {formatDuration(script.durationMinutes)}
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={6}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Category fontSize="small" />
                      <Typography variant="body2">
                        {script.category}
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={6}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Visibility fontSize="small" />
                      <Typography variant="body2">
                        {script.playCount} vues
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={6}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Star fontSize="small" />
                      <Typography variant="body2">
                        {script.rating.toFixed(1)} ({script.ratingCount})
                      </Typography>
                    </Box>
                  </Grid>
                </Grid>

                <Divider sx={{ my: 2 }} />

                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    Tags
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                    {script.tags.map((tag) => (
                      <Chip key={tag} label={tag} size="small" variant="outlined" />
                    ))}
                  </Box>
                </Box>

                <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
                  {script.isPremium && (
                    <Chip label="Premium" color="warning" size="small" />
                  )}
                  <Chip label={`v${script.version}`} size="small" variant="outlined" />
                </Box>

                <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                  Mis à jour: {new Date(script.updatedAt).toLocaleDateString()}
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          {/* Lecteur et contenu */}
          <Grid item xs={12} md={8}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Lecteur de Script
                </Typography>

                {/* Contrôles de lecture */}
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                  <IconButton
                    onClick={simulatePlayback}
                    color="primary"
                    size="large"
                  >
                    {isPlaying ? <Pause /> : <PlayArrow />}
                  </IconButton>
                  <IconButton onClick={stopPlayback} disabled={!isPlaying && playbackProgress === 0}>
                    <Stop />
                  </IconButton>
                  <Box sx={{ flexGrow: 1, mx: 2 }}>
                    <LinearProgress
                      variant="determinate"
                      value={playbackProgress}
                      sx={{ height: 8, borderRadius: 4 }}
                    />
                  </Box>
                  <Typography variant="body2" color="text.secondary">
                    {Math.round(playbackProgress)}%
                  </Typography>
                </Box>

                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Segment {currentSegment + 1} / {script.script.length}
                </Typography>

                {/* Contenu du script */}
                <Box sx={{ maxHeight: 400, overflow: 'auto' }}>
                  <List>
                    {script.script.map((segment, index) => (
                      <ListItem
                        key={index}
                        sx={{
                          bgcolor: index === currentSegment ? 'action.selected' : 'transparent',
                          borderRadius: 1,
                          mb: 1,
                        }}
                      >
                        <ListItemText
                          primary={
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                              <Typography variant="body2" color="primary">
                                Segment {index + 1}
                              </Typography>
                              <Chip
                                label={`${segment.pause}ms`}
                                size="small"
                                variant="outlined"
                                icon={<Timer />}
                              />
                              <Chip
                                label={`${segment.rate}x`}
                                size="small"
                                variant="outlined"
                                icon={<Speed />}
                              />
                              <Chip
                                label={`Pitch: ${segment.pitch}`}
                                size="small"
                                variant="outlined"
                                icon={<VolumeUp />}
                              />
                            </Box>
                          }
                          secondary={
                            <Typography variant="body1" sx={{ mt: 1 }}>
                              {segment.text}
                            </Typography>
                          }
                        />
                      </ListItem>
                    ))}
                  </List>
                </Box>

                {script.script.length === 0 && (
                  <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', py: 4 }}>
                    Aucun contenu de script disponible
                  </Typography>
                )}
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose}>
          Fermer
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ScriptPreview;
