import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  Grid,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  Tabs,
  Tab,
  Alert,
  CircularProgress,
  Tooltip,
} from '@mui/material';
import {
  Add,
  Edit,
  Delete,
  PlayArrow,
  Visibility,
  CloudUpload,
  Download,
  Refresh,
} from '@mui/icons-material';
import { useAuth } from '@/contexts/AuthContext';
import { scriptService } from '@/services/scriptService';
import ScriptEditor from '@/components/Scripts/ScriptEditor';
import ScriptPreview from '@/components/Scripts/ScriptPreview';

interface Script {
  id: string;
  title: string;
  description: string;
  type: 'hypnosis' | 'meditation' | 'training' | 'visualization' | 'story' | 'silence';
  language: 'fr' | 'en' | 'es';
  durationMinutes: number;
  category: string;
  tags: string[];
  isPremium: boolean;
  version: number;
  updatedAt: string;
  playCount: number;
  rating: number;
}

const SCRIPT_TYPES = [
  { value: 'hypnosis', label: 'Hypnose' },
  { value: 'meditation', label: 'Méditation' },
  { value: 'training', label: 'Formation' },
  { value: 'visualization', label: 'Visualisation' },
  { value: 'story', label: 'Histoire' },
  { value: 'silence', label: 'Silence' },
];

const LANGUAGES = [
  { value: 'fr', label: 'Français' },
  { value: 'en', label: 'English' },
  { value: 'es', label: 'Español' },
];

const Scripts: React.FC = () => {
  const { hasPermission } = useAuth();
  const [scripts, setScripts] = useState<Script[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedLanguage, setSelectedLanguage] = useState<'fr' | 'en' | 'es'>('fr');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedType, setSelectedType] = useState<string>('all');
  const [selectedScript, setSelectedScript] = useState<Script | null>(null);
  const [isEditorOpen, setIsEditorOpen] = useState(false);
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [scriptToDelete, setScriptToDelete] = useState<Script | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Vérifier les permissions
  const canManageScripts = hasPermission('manage_sessions');

  useEffect(() => {
    loadScripts();
  }, [selectedLanguage]);

  const loadScripts = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await scriptService.getScriptsByLanguage(selectedLanguage);
      setScripts(data);
    } catch (error: any) {
      setError(`Erreur lors du chargement des scripts: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateScript = () => {
    setSelectedScript(null);
    setIsEditorOpen(true);
  };

  const handleEditScript = (script: Script) => {
    setSelectedScript(script);
    setIsEditorOpen(true);
  };

  const handlePreviewScript = (script: Script) => {
    setSelectedScript(script);
    setIsPreviewOpen(true);
  };

  const handleDeleteScript = (script: Script) => {
    setScriptToDelete(script);
    setIsDeleteDialogOpen(true);
  };

  const confirmDeleteScript = async () => {
    if (!scriptToDelete) return;

    try {
      await scriptService.deleteScript(scriptToDelete.language, scriptToDelete.id);
      setSuccess(`Script "${scriptToDelete.title}" supprimé avec succès`);
      setScripts(scripts.filter(s => s.id !== scriptToDelete.id));
    } catch (error: any) {
      setError(`Erreur lors de la suppression: ${error.message}`);
    } finally {
      setIsDeleteDialogOpen(false);
      setScriptToDelete(null);
    }
  };

  const handleScriptSaved = (savedScript: Script) => {
    setSuccess(`Script "${savedScript.title}" sauvegardé avec succès`);
    loadScripts(); // Recharger la liste
    setIsEditorOpen(false);
  };

  const handleSyncToFirestore = async () => {
    try {
      setLoading(true);
      await scriptService.syncAllScriptsToFirestore();
      setSuccess('Synchronisation avec Firestore terminée');
      loadScripts();
    } catch (error: any) {
      setError(`Erreur lors de la synchronisation: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  // Filtrer les scripts
  const filteredScripts = scripts.filter(script => {
    const matchesSearch = script.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         script.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = selectedType === 'all' || script.type === selectedType;
    return matchesSearch && matchesType;
  });

  const getTypeColor = (type: string) => {
    const colors: Record<string, 'primary' | 'secondary' | 'success' | 'warning' | 'info' | 'error'> = {
      hypnosis: 'primary',
      meditation: 'success',
      training: 'warning',
      visualization: 'info',
      story: 'secondary',
      silence: 'error',
    };
    return colors[type] || 'default';
  };

  if (!canManageScripts) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">
          Vous n'avez pas les permissions nécessaires pour gérer les scripts.
        </Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          Gestion des Scripts PiKnowKyo
        </Typography>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Tooltip title="Synchroniser avec Firestore">
            <IconButton onClick={handleSyncToFirestore} disabled={loading}>
              <CloudUpload />
            </IconButton>
          </Tooltip>
          <Tooltip title="Actualiser">
            <IconButton onClick={loadScripts} disabled={loading}>
              <Refresh />
            </IconButton>
          </Tooltip>
          <Button
            variant="contained"
            startIcon={<Add />}
            onClick={handleCreateScript}
            disabled={loading}
          >
            Nouveau Script
          </Button>
        </Box>
      </Box>

      {/* Messages */}
      {error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}
      {success && (
        <Alert severity="success" sx={{ mb: 2 }} onClose={() => setSuccess(null)}>
          {success}
        </Alert>
      )}

      {/* Filtres */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} sm={3}>
              <Tabs
                value={selectedLanguage}
                onChange={(_, value) => setSelectedLanguage(value)}
                variant="fullWidth"
              >
                {LANGUAGES.map(lang => (
                  <Tab key={lang.value} label={lang.label} value={lang.value} />
                ))}
              </Tabs>
            </Grid>
            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                label="Rechercher"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                size="small"
              />
            </Grid>
            <Grid item xs={12} sm={3}>
              <FormControl fullWidth size="small">
                <InputLabel>Type</InputLabel>
                <Select
                  value={selectedType}
                  onChange={(e) => setSelectedType(e.target.value)}
                  label="Type"
                >
                  <MenuItem value="all">Tous les types</MenuItem>
                  {SCRIPT_TYPES.map(type => (
                    <MenuItem key={type.value} value={type.value}>
                      {type.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={2}>
              <Typography variant="body2" color="text.secondary">
                {filteredScripts.length} script(s)
              </Typography>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Liste des scripts */}
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
          <CircularProgress />
        </Box>
      ) : (
        <Grid container spacing={2}>
          {filteredScripts.map((script) => (
            <Grid item xs={12} sm={6} md={4} key={script.id}>
              <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                <CardContent sx={{ flexGrow: 1 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
                    <Typography variant="h6" component="h2" sx={{ flexGrow: 1, mr: 1 }}>
                      {script.title}
                    </Typography>
                    <Chip
                      label={script.type}
                      color={getTypeColor(script.type)}
                      size="small"
                    />
                  </Box>
                  
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    {script.description.substring(0, 100)}...
                  </Typography>

                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mb: 2 }}>
                    <Chip label={`${script.durationMinutes}min`} size="small" variant="outlined" />
                    {script.isPremium && (
                      <Chip label="Premium" size="small" color="warning" variant="outlined" />
                    )}
                    <Chip label={`v${script.version}`} size="small" variant="outlined" />
                  </Box>

                  <Typography variant="caption" color="text.secondary">
                    Mis à jour: {new Date(script.updatedAt).toLocaleDateString()}
                  </Typography>
                </CardContent>

                <Box sx={{ p: 1, display: 'flex', justifyContent: 'space-between' }}>
                  <Box>
                    <Tooltip title="Prévisualiser">
                      <IconButton size="small" onClick={() => handlePreviewScript(script)}>
                        <Visibility />
                      </IconButton>
                    </Tooltip>
                  </Box>
                  <Box>
                    <Tooltip title="Modifier">
                      <IconButton size="small" onClick={() => handleEditScript(script)}>
                        <Edit />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Supprimer">
                      <IconButton size="small" onClick={() => handleDeleteScript(script)} color="error">
                        <Delete />
                      </IconButton>
                    </Tooltip>
                  </Box>
                </Box>
              </Card>
            </Grid>
          ))}
        </Grid>
      )}

      {/* Dialog de suppression */}
      <Dialog open={isDeleteDialogOpen} onClose={() => setIsDeleteDialogOpen(false)}>
        <DialogTitle>Confirmer la suppression</DialogTitle>
        <DialogContent>
          <Typography>
            Êtes-vous sûr de vouloir supprimer le script "{scriptToDelete?.title}" ?
            Cette action est irréversible.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setIsDeleteDialogOpen(false)}>Annuler</Button>
          <Button onClick={confirmDeleteScript} color="error" variant="contained">
            Supprimer
          </Button>
        </DialogActions>
      </Dialog>

      {/* Éditeur de script */}
      <ScriptEditor
        open={isEditorOpen}
        onClose={() => setIsEditorOpen(false)}
        script={selectedScript}
        language={selectedLanguage}
        onSave={handleScriptSaved}
      />

      {/* Prévisualisation de script */}
      <ScriptPreview
        open={isPreviewOpen}
        onClose={() => setIsPreviewOpen(false)}
        script={selectedScript}
      />
    </Box>
  );
};

export default Scripts;
