# 🧘‍♀️ PiKnowKyo - Système de Gestion des Scripts

Système complet de gestion des scripts d'hypnose et méditation avec interface d'administration web et synchronisation Firestore en temps réel.

## 🏗️ Architecture du Système

```
PiKnowKyo/
├── piknowkyo/          # Application principale (React Native/Web)
│   ├── src/
│   ├── public/assets/sessionScripts/  # Scripts locaux (backup)
│   └── scripts/        # Scripts de synchronisation
├── Pmanager/           # Interface d'administration
│   ├── src/pages/Scripts.tsx
│   ├── src/services/scriptService.ts
│   └── src/components/Scripts/
└── Firestore           # Base de données cloud
    ├── scripts/        # Collection des scripts
    └── manifests/      # Manifestes par langue
```

## 🚀 Démarrage Rapide

### **Option 1 : Démarrage automatique (Windows)**
```bash
start-dev.bat
```

### **Option 2 : Démarrage automatique (Linux/Mac)**
```bash
./start-dev.sh
```

### **Option 3 : Démarrage manuel**
```bash
# Terminal 1 - Application principale
cd piknowkyo
npm run dev

# Terminal 2 - Interface d'administration
cd Pmanager
npm run dev
```

**URLs d'accès :**
- 🎵 **PiKnowKyo App** : http://localhost:3000
- ⚙️ **Interface Admin** : http://localhost:3001

## 📋 Fonctionnalités Principales

### 🎯 **Interface d'Administration (Pmanager)**
- ✅ **Créer** de nouveaux scripts
- ✅ **Modifier** les scripts existants avec versioning automatique
- ✅ **Supprimer** des scripts
- ✅ **Prévisualiser** avec lecteur intégré
- ✅ **Synchroniser** avec Firestore en temps réel
- ✅ **Filtrer** par langue, type, recherche
- ✅ **Gérer** les tags et catégories

### 🔄 **Synchronisation Automatique**
- ✅ **Upload initial** : 140+ scripts uploadés vers Firestore
- ✅ **Mise à jour temps réel** : Modifications synchronisées instantanément
- ✅ **Versioning** : Chaque modification incrémente la version
- ✅ **Manifestes** : Mis à jour automatiquement pour chaque langue

### 📱 **Application Utilisateur (PiKnowKyo)**
- ✅ **Chargement dynamique** depuis Firestore
- ✅ **Fallback local** si Firestore indisponible
- ✅ **Synchronisation** automatique des nouvelles versions
- ✅ **Cache intelligent** pour les performances

## 🛠️ Workflow de Gestion des Scripts

### **1. Créer un nouveau script**
```
Interface Admin → Nouveau Script → Édition → Sauvegarde → Firestore → Utilisateurs
```

### **2. Modifier un script existant**
```
Interface Admin → Modifier → Changements → Sauvegarde → Version++ → Firestore → Utilisateurs
```

### **3. Synchronisation en ligne de commande**
```bash
# Mettre à jour un script spécifique
cd piknowkyo
node scripts/updateScript.js fr mon_script

# Upload initial de tous les scripts
node scripts/uploadScriptsToFirestore.js

# Vérifier l'état de Firestore
node scripts/verifyFirestoreUpload.js
```

## 📊 Organisation des Données

### **Collections Firestore**

#### `scripts` - Scripts individuels
```json
{
  "id": "fr_accepter_et_rayonner",
  "title": "Accepter et Rayonner",
  "type": "hypnosis",
  "language": "fr",
  "category": "développement-personnel",
  "tags": ["acceptation", "confiance-en-soi"],
  "version": 2,
  "script": [
    {
      "text": "Fermez les yeux...",
      "pause": 2000,
      "pitch": 1.0,
      "rate": 0.8
    }
  ]
}
```

#### `manifests` - Manifestes par langue
```json
{
  "language": "fr",
  "totalSessions": 48,
  "sessions": [
    {
      "id": "accepter_et_rayonner",
      "title": "Accepter et Rayonner",
      "type": "hypnosis",
      "estimatedDuration": 45
    }
  ]
}
```

### **Catégories Automatiques**
- 🧠 **développement-personnel** : estime-de-soi, confiance-en-soi, motivation
- 🌿 **santé-bien-être** : relaxation, sommeil, guérison
- 💝 **émotionnel** : deuil, acceptation, libération
- 🚫 **addictions-blocages** : addictions, blocages-mentaux, phobies
- 🍎 **alimentation** : gestion-du-poids, alimentation-saine
- 💰 **abondance-finances** : abondance, richesse, prospérité
- ✨ **spiritualité** : chakras, énergie, méditation
- 👶 **enfants** : enfants, hypnose-enfants, tdah
- 🧩 **mémoire-concentration** : mémoire, concentration, clarté

## 🔧 Scripts de Gestion

### **Scripts Node.js (piknowkyo/scripts/)**

#### `uploadScriptsToFirestore.js`
```bash
node scripts/uploadScriptsToFirestore.js
```
- Upload initial de tous les scripts
- Création des manifestes
- Organisation par catégories

#### `updateScript.js`
```bash
node scripts/updateScript.js <langue> <scriptId>
# Exemple: node scripts/updateScript.js fr accepter_et_rayonner
```
- Mise à jour d'un script spécifique
- Versioning automatique
- Synchronisation Firestore

#### `verifyFirestoreUpload.js`
```bash
node scripts/verifyFirestoreUpload.js
```
- Vérification de l'état de Firestore
- Statistiques par langue
- Validation des données

## 🎨 Interface d'Administration

### **Page Scripts (/scripts)**
- 📋 **Liste des scripts** avec filtres avancés
- 🔍 **Recherche** par titre/description
- 🏷️ **Filtrage** par langue, type, catégorie
- 📊 **Statistiques** en temps réel

### **Éditeur de Scripts**
- 📝 **Onglet Métadonnées** : titre, description, tags, type
- 🎵 **Onglet Script** : segments TTS avec paramètres
- 💾 **Sauvegarde automatique** avec versioning
- 🔄 **Synchronisation** Firestore instantanée

### **Lecteur de Prévisualisation**
- ▶️ **Simulation de lecture** avec contrôles
- 📊 **Affichage des paramètres** TTS
- 🎯 **Navigation par segments**
- 📈 **Barre de progression**

## 🔐 Sécurité et Permissions

### **Authentification**
- 🔒 **Firebase Auth** pour l'interface admin
- 👤 **Permissions granulaires** (`manage_sessions`)
- 🛡️ **Protection des routes** côté client et serveur

### **Règles Firestore**
```javascript
// Lecture publique, écriture admin uniquement
match /scripts/{document} {
  allow read: if true;
  allow write: if request.auth != null && 
    get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
}
```

## 📈 Statistiques et Monitoring

### **Métriques Disponibles**
- 📊 **Nombre total de scripts** par langue
- 📈 **Répartition par type** (hypnose, méditation, etc.)
- 🏷️ **Distribution par catégorie**
- 👀 **Statistiques d'utilisation** (vues, notes)

### **Monitoring**
- 🔄 **Synchronisation en temps réel**
- ⚠️ **Alertes d'erreur** dans la console
- 📝 **Logs détaillés** pour le debugging

## 🚨 Dépannage

### **Problèmes courants**

#### Erreur d'index Firestore
```
Solution: Créer l'index composite via le lien dans l'erreur
```

#### Script non visible côté utilisateur
```
1. Vérifier la synchronisation Firestore
2. Forcer la synchronisation via l'interface admin
3. Vérifier les manifestes
```

#### Erreur de permissions
```
1. Vérifier l'authentification Firebase
2. Vérifier la permission 'manage_sessions'
3. Vérifier les règles Firestore
```

## 🎯 Roadmap

### **Fonctionnalités à venir**
- 🎨 **Éditeur visuel** de scripts
- 🎵 **Prévisualisation audio** avec TTS
- 📊 **Analytics avancées** d'utilisation
- 🌍 **Traduction automatique** entre langues
- 📱 **Application mobile** d'administration
- 🤖 **IA pour génération** de scripts

## 📞 Support

### **Documentation**
- 📖 **README_SCRIPTS.md** : Guide détaillé de l'interface admin
- 📋 **Scripts Node.js** : Documentation inline
- 🔧 **Code commenté** : Explications détaillées

### **Contact**
Pour toute question ou problème, consultez les logs de la console ou contactez l'équipe de développement.

---

**🧘‍♀️ PiKnowKyo - Plateforme complète de gestion des scripts d'hypnose et méditation** ✨
