# Guide d'intégration d'un nouveau Mini-Jeu

Ce guide détaille l'architecture et les étapes nécessaires pour ajouter un nouveau mini-jeu à l'application. L'objectif est de maintenir une structure modulaire, d'assurer la réutilisabilité des composants et de faciliter la gestion de la progression utilisateur et des classements.

## 1. Rappel de l'Architecture des Jeux

Tous les jeux seront organisés sous le répertoire `src/games/`. Chaque jeu aura son propre sous-répertoire, et des utilitaires/composants communs seront partagés.

```
src/
├── pages/
│   └── GamesPage.tsx     # Page principale listant les jeux
├── components/
│   └── ReusableModal.tsx # Modale générique (utilisée aussi pour les jeux)
├── games/
│   ├── [nom-du-jeu]/    # Ex: zen-tetris/
│   │   ├── GameComponent.tsx # Composant React principal du jeu
│   │   ├── logic.ts          # Logique pure du jeu (sans React)
│   │   ├── assets/           # Images, sons spécifiques au jeu
│   │   └── styles.ts         # Styled-components spécifiques au jeu
│   └── common/           # Utilitaires et interfaces communes à tous les jeux
│       ├── models.ts         # Interfaces TypeScript partagées
│       ├── gameUtils.ts      # Fonctions de sauvegarde, chargement, leaderboard
│       └── components/       # Composants React génériques pour les jeux
│           ├── GameTimer.tsx
│           └── ScoreDisplay.tsx
```

## 2. Interfaces et Modèles Communs (`src/games/common/models.ts`)

Pour assurer une cohérence et une bonne typification, définissez les interfaces suivantes :

```typescript
// src/games/common/models.ts

import React from 'react';

// --- Interfaces pour la page GamesPage ---
export interface GameInfo {
  id: string; // ID unique du jeu (ex: "zen-tetris", "memory-challenge")
  titleKey: string; // Clé i18next pour le titre du jeu (ex: "games.zenTetris.title")
  descriptionKey: string; // Clé i18next pour la description du jeu
  thumbnailUrl: string; // URL de l'image de la carte du jeu
  component: React.FC<GameProps>; // Le composant React principal du jeu
  maxLevels: number; // Nombre total de niveaux pour ce jeu
  estimatedDurationMinutes: number; // Durée estimée d'une partie (optionnel)
  tags?: string[]; // Mots-clés pour le jeu (optionnel)
  // ... toute autre métadonnée utile pour l'affichage ou la logique globale
}

// --- Interfaces pour les props passées à un GameComponent ---
export interface GameProps {
  userId: string; // ID de l'utilisateur connecté pour la sauvegarde
  onGameEnd: (score: number, levelReached: number, finalTimeSeconds: number) => void;
  onGameQuit: () => void; // Callback appelé quand l'utilisateur quitte le jeu (sans le terminer)
  onPauseChange: (isPaused: boolean) => void; // Notifier le conteneur de l'état de pause
  initialGameState?: SavedGameState; // État de jeu à charger pour une partie reprise
}

// --- Interfaces pour la sauvegarde de la progression ---
export interface SavedGameState {
  score: number;
  level: number;
  currentTimerSeconds: number; // Temps écoulé dans le niveau actuel ou total
  specificGameState: any; // Données spécifiques au jeu (ex: état de la grille Tetris, cartes retournées)
  lastPlayed: Date; // Date de la dernière sauvegarde
}

// --- Autres interfaces spécifiques aux utilitaires de jeu ---
export interface LeaderboardEntry {
  userId: string;
  gameId: string;
  score: number;
  levelReached: number;
  timestamp: Date;
  // ... d'autres champs pour le classement
}
```

## 3. Utilitaires Communs de Jeu (`src/games/common/gameUtils.ts`)

Ces fonctions géreront la persistance des données de jeu et la soumission des scores. Elles utiliseront `localStorage` pour la persistance côté client, mais devraient être adaptées pour un backend réel si vous en avez un.

```typescript
// src/games/common/gameUtils.ts

import { SavedGameState, LeaderboardEntry } from './models';

const LOCAL_STORAGE_PREFIX = 'game_save_';
const LEADERBOARD_PREFIX = 'game_leaderboard_'; // Pour une simulation de leaderboard local

/**
 * Sauvegarde l'état actuel d'un jeu pour un utilisateur.
 * @param gameId ID unique du jeu (ex: "zen-tetris")
 * @param userId ID de l'utilisateur
 * @param gameState L'objet SavedGameState à sauvegarder
 */
export const saveGameState = (gameId: string, userId: string, gameState: SavedGameState): void => {
  try {
    const key = `${LOCAL_STORAGE_PREFIX}${userId}_${gameId}`;
    localStorage.setItem(key, JSON.stringify(gameState));
    console.log(`Progression du jeu ${gameId} sauvegardée pour ${userId}.`);
  } catch (error) {
    console.error(`Erreur lors de la sauvegarde de la progression du jeu ${gameId}:`, error);
  }
};

/**
 * Charge l'état sauvegardé d'un jeu pour un utilisateur.
 * @param gameId ID unique du jeu
 * @param userId ID de l'utilisateur
 * @returns L'objet SavedGameState ou null si aucune sauvegarde n'existe.
 */
export const loadGameState = (gameId: string, userId: string): SavedGameState | null => {
  try {
    const key = `${LOCAL_STORAGE_PREFIX}${userId}_${gameId}`;
    const saved = localStorage.getItem(key);
    if (saved) {
      console.log(`Progression du jeu ${gameId} chargée pour ${userId}.`);
      const parsed = JSON.parse(saved);
      // Assurez-vous que lastPlayed est bien un objet Date
      if (parsed.lastPlayed) parsed.lastPlayed = new Date(parsed.lastPlayed);
      return parsed as SavedGameState;
    }
  } catch (error) {
    console.error(`Erreur lors du chargement de la progression du jeu ${gameId}:`, error);
  }
  return null;
};

/**
 * Efface l'état sauvegardé d'un jeu pour un utilisateur.
 * @param gameId ID unique du jeu
 * @param userId ID de l'utilisateur
 */
export const clearGameState = (gameId: string, userId: string): void => {
  try {
    const key = `${LOCAL_STORAGE_PREFIX}${userId}_${gameId}`;
    localStorage.removeItem(key);
    console.log(`Progression du jeu ${gameId} effacée pour ${userId}.`);
  } catch (error) {
    console.error(`Erreur lors de l'effacement de la progression du jeu ${gameId}:`, error);
  }
};

/**
 * Soumet le score d'une partie terminée au leaderboard (simulation locale).
 * Dans une application réelle, cela ferait un appel API à votre backend.
 * @param entry L'entrée LeaderboardEntry à soumettre.
 */
export const submitScoreToLeaderboard = (entry: Omit<LeaderboardEntry, 'timestamp'> & {gameId: string}): void => {
    try {
        const fullEntry: LeaderboardEntry = { ...entry, timestamp: new Date() };
        const key = `${LEADERBOARD_PREFIX}${fullEntry.gameId}`;
        const existingEntries: LeaderboardEntry[] = JSON.parse(localStorage.getItem(key) || '[]');
        
        // Ajouter la nouvelle entrée
        existingEntries.push(fullEntry);
        
        // Pour une simulation simple, trier et ne garder que les 10 meilleurs scores par jeu
        existingEntries.sort((a, b) => b.score - a.score || b.levelReached - a.levelReached);
        const topScores = existingEntries.slice(0, 10); // Garder les 10 meilleurs
        
        localStorage.setItem(key, JSON.stringify(topScores));
        console.log(`Score soumis au leaderboard pour ${entry.gameId}:`, fullEntry);
    } catch (error) {
        console.error("Erreur lors de la soumission du score au leaderboard:", error);
    }
};

/**
 * Récupère le leaderboard pour un jeu donné (simulation locale).
 * @param gameId ID unique du jeu
 * @returns Tableau des entrées du leaderboard.
 */
export const getLeaderboard = (gameId: string): LeaderboardEntry[] => {
    try {
        const key = `${LEADERBOARD_PREFIX}${gameId}`;
        return JSON.parse(localStorage.getItem(key) || '[]');
    } catch (error) {
        console.error("Erreur lors de la récupération du leaderboard:", error);
        return [];
    }
};

/**
 * Récupère le meilleur score personnel pour un jeu donné et un utilisateur.
 * @param gameId ID unique du jeu
 * @param userId ID de l'utilisateur
 * @returns Le meilleur score ou 0 si aucun.
 */
export const getPersonalBestScore = (gameId: string, userId: string): number => {
    const leaderboard = getLeaderboard(gameId);
    const personalEntries = leaderboard.filter(entry => entry.userId === userId);
    if (personalEntries.length === 0) return 0;
    return Math.max(...personalEntries.map(entry => entry.score));
};
```

## 4. Composants Communs pour les Jeux (`src/games/common/components/`)

### `GameModal.tsx` (modale pour les règles et le menu pause)

Ceci sera une extension de `ReusableModal`, avec des props spécifiques pour la logique de jeu.

```typescript
// src/games/common/components/GameModal.tsx

import React from 'react';
import styled from 'styled-components';
import ReusableModal from '../../../components/ReusableModal'; // Assurez-vous du chemin
import { FiPlay, FiCornerDownLeft, FiRefreshCcw, FiMenu } from 'react-icons/fi';
import { useTranslation } from 'react-i18next';

// Définissez un styled component pour les boutons de la modale
const ModalActions = styled.div`
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-top: 1.5rem;

  button {
    background: ${({ theme }) => theme.primary};
    color: ${({ theme }) => theme.textLight};
    border: none;
    border-radius: 8px;
    padding: 0.8rem 1.5rem;
    font-size: 1rem;
    cursor: pointer;
    transition: background-color 0.2s, transform 0.1s;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;

    &:hover { opacity: 0.9; }
    &:active { transform: scale(0.98); }
    &:disabled { opacity: 0.6; cursor: not-allowed; }

    &.secondary {
        background: ${({ theme }) => theme.surfaceAlt};
        color: ${({ theme }) => theme.text};
        border: 1px solid ${({ theme }) => theme.border};
    }
    &.danger {
        background: ${({ theme }) => theme.errorColor};
    }
  }
`;

interface GameModalProps {
  isOpen: boolean;
  title: string;
  children: React.ReactNode; // Contenu de la modale (règles, etc.)
  
  // Boutons spécifiques à la logique du jeu
  showStartButton?: boolean;
  showResumeButton?: boolean;
  showReturnButton?: boolean;
  showRestartButton?: boolean; // Pour le menu pause

  onStart?: () => void;
  onResume?: () => void;
  onReturn?: () => void;
  onRestart?: () => void;

  isLoading?: boolean; // Si la modale doit montrer un état de chargement (ex: sauvegarde)
}

const GameModal: React.FC<GameModalProps> = ({
  isOpen, title, children,
  showStartButton, showResumeButton, showReturnButton, showRestartButton,
  onStart, onResume, onReturn, onRestart,
  isLoading
}) => {
  const { t } = useTranslation();

  return (
    <ReusableModal
      isOpen={isOpen}
      onClose={() => { if (showReturnButton && onReturn && !isLoading) onReturn(); }} // Ferme par retour si retour possible
      title={title}
      titleIcon={<FiMenu />} // Icône générique pour le menu de jeu
      isLoading={isLoading}
      footerContent={
        <ModalActions>
          {showReturnButton && (
            <button onClick={onReturn} className="secondary" disabled={isLoading}>
              <FiCornerDownLeft /> {t('game.modal.return', 'Retour')}
            </button>
          )}
          {showRestartButton && (
            <button onClick={onRestart} className="secondary" disabled={isLoading}>
              <FiRefreshCcw /> {t('game.modal.restart', 'Redémarrer')}
            </button>
          )}
          {showResumeButton && (
            <button onClick={onResume} disabled={isLoading}>
              <FiPlay /> {t('game.modal.resume', 'Reprendre')}
            </button>
          )}
          {showStartButton && (
            <button onClick={onStart} disabled={isLoading}>
              <FiPlay /> {t('game.modal.start', 'Commencer')}
            </button>
          )}
        </ModalActions>
      }
    >
      {children}
    </ReusableModal>
  );
};

export default GameModal;
```

### `GameTimer.tsx` et `ScoreDisplay.tsx`

Ces composants sont des afficheurs simples. Leur logique sera gérée par le `GameComponent` parent.

```typescript
// src/games/common/components/GameTimer.tsx
import React from 'react';
import styled from 'styled-components';
import { FiClock } from 'react-icons/fi';

const TimerContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.2rem;
  font-weight: bold;
  color: ${({ theme }) => theme.textLight || 'white'};
`;

interface GameTimerProps {
  timeInSeconds: number;
}

const GameTimer: React.FC<GameTimerProps> = ({ timeInSeconds }) => {
  const minutes = Math.floor(timeInSeconds / 60);
  const seconds = timeInSeconds % 60;
  return (
    <TimerContainer>
      <FiClock /> {String(minutes).padStart(2, '0')}:{String(seconds).padStart(2, '0')}
    </TimerContainer>
  );
};

export default GameTimer;
```

```typescript
// src/games/common/components/ScoreDisplay.tsx
import React from 'react';
import styled from 'styled-components';
import { FiTrophy } from 'react-icons/fi';

const ScoreContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.2rem;
  font-weight: bold;
  color: ${({ theme }) => theme.textLight || 'white'};
`;

interface ScoreDisplayProps {
  score: number;
}

const ScoreDisplay: React.FC<ScoreDisplayProps> = ({ score }) => {
  return (
    <ScoreContainer>
      <FiTrophy /> {score}
    </ScoreContainer>
  );
};

export default ScoreDisplay;
```

## 5. Création d'un Nouveau Jeu (`src/games/[nom-du-jeu]/`)

Pour chaque nouveau jeu, suivez ces étapes :

### Étape 5.1 : Créer le Répertoire du Jeu

Créez un nouveau dossier sous `src/games/` avec un `id` unique et en kebab-case (ex: `zen-tetris`).

### Étape 5.2 : Développer la Logique du Jeu (`logic.ts`)

Créez un fichier `logic.ts` (ou `engine.ts`) qui contient toute la logique métier du jeu.
**Principes :**
*   **Pureté :** Cette logique ne doit pas avoir de dépendances React (pas de `useState`, `useEffect`, `useRef` ici).
*   **Fonctions et Classes :** Utilisez des fonctions pures ou des classes qui manipulent l'état du jeu et retournent de nouveaux états.
*   **Inputs/Outputs :** Les fonctions doivent prendre l'état actuel du jeu et les actions du joueur comme entrées, et retourner le nouvel état du jeu.

**Exemple (simplifié pour Tetris) :**

```typescript
// src/games/zen-tetris/logic.ts

export interface TetrisGameState {
  grid: number[][]; // Représentation de la grille
  currentPiece: any; // Pièce en cours
  nextPiece: any;
  score: number;
  level: number;
  linesCleared: number;
  gameOver: boolean;
  // ... tout état nécessaire au jeu
}

export const initializeTetrisGame = (initialLevel: number = 1): TetrisGameState => {
  // Logique pour créer une nouvelle grille vide, la première pièce, etc.
  return {
    grid: Array(20).fill(0).map(() => Array(10).fill(0)),
    currentPiece: null, // Logique pour générer la première pièce
    nextPiece: null,
    score: 0,
    level: initialLevel,
    linesCleared: 0,
    gameOver: false,
  };
};

export const movePiece = (state: TetrisGameState, direction: 'left' | 'right' | 'down'): TetrisGameState => {
  // Logique pour déplacer la pièce et retourner le nouvel état
  // ...
  return { ...state };
};

export const rotatePiece = (state: TetrisGameState): TetrisGameState => {
  // Logique pour faire pivoter la pièce
  // ...
  return { ...state };
};

export const updateGame = (state: TetrisGameState, dt: number): TetrisGameState => {
  // Logique de mise à jour du jeu par intervalle de temps (chute de pièce, etc.)
  // Inclure la détection de lignes complétées et la mise à jour du score/niveau
  // ...
  return { ...state };
};

// ... autres fonctions logiques (checkCollision, clearLines, etc.)
```

### Étape 5.3 : Créer le Composant React du Jeu (`GameComponent.tsx`)

C'est l'interface entre la logique du jeu et l'utilisateur.

```typescript
// src/games/zen-tetris/GameComponent.tsx

import React, { useState, useEffect, useRef, useCallback } from 'react';
import styled from 'styled-components';
import { useTranslation } from 'react-i18next';
import { GameProps, SavedGameState } from '../common/models';
import { initializeTetrisGame, updateGame, TetrisGameState, movePiece, rotatePiece } from './logic'; // Importer votre logique
import { saveGameState, clearGameState, submitScoreToLeaderboard } from '../common/gameUtils';
import GameModal from '../common/components/GameModal'; // Importer le composant GameModal
import GameTimer from '../common/components/GameTimer';
import ScoreDisplay from '../common/components/ScoreDisplay';

// Styles spécifiques au jeu (importés de ./styles.ts)
const GameContainer = styled.div`
  /* ... Styles spécifiques au Tetris ... */
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
  padding: 1rem;
  background-color: #2c3e50; /* Exemple de fond */
  color: white;
`;

const GameBoard = styled.div`
  /* ... Styles de la grille de Tetris ... */
  display: grid;
  grid-template-rows: repeat(20, 20px);
  grid-template-columns: repeat(10, 20px);
  border: 2px solid #ecf0f1;
`;

const Cell = styled.div<{ $isFilled: boolean }>`
  width: 20px;
  height: 20px;
  background-color: ${({ $isFilled }) => ($isFilled ? '#3498db' : '#34495e')};
  border: 1px solid rgba(0,0,0,0.2);
`;

const Controls = styled.div`
  margin-top: 1rem;
  display: flex;
  gap: 0.5rem;
  button {
      padding: 0.5rem 1rem;
      background: #3498db;
      color: white;
      border: none;
      border-radius: 5px;
      cursor: pointer;
  }
`;

const UIHeader = styled.div`
  display: flex;
  justify-content: space-between;
  width: 100%;
  max-width: 300px; /* Largeur de la grille */
  margin-bottom: 1rem;
`;

const PauseButton = styled.button`
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(0,0,0,0.5);
  color: white;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  cursor: pointer;
  z-index: 10;
`;

const GameComponent: React.FC<GameProps> = ({
  userId, onGameEnd, onGameQuit, onPauseChange, initialGameState
}) => {
  const { t } = useTranslation();

  // États du jeu (affichés ou contrôlant le rendu)
  const [gameState, setGameState] = useState<TetrisGameState>(() => {
    if (initialGameState && initialGameState.specificGameState) {
      return initialGameState.specificGameState;
    }
    return initializeTetrisGame(); // Initialisation au niveau 1 par défaut
  });
  const [isPaused, setIsPaused] = useState(false);
  const [showRulesModal, setShowRulesModal] = useState(!initialGameState); // Afficher les règles si pas de sauvegarde
  const [currentTimerSeconds, setCurrentTimerSeconds] = useState(initialGameState?.currentTimerSeconds || 0);

  // Référence pour la logique du jeu (éviter les closures Stale)
  const gameLogicRef = useRef(gameState);
  gameLogicRef.current = gameState; // Toujours à jour

  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const gameIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // --- Fonctions de gestion du jeu ---

  const startGame = useCallback(() => {
    setShowRulesModal(false);
    setIsPaused(false);
    onPauseChange(false);
    // Réinitialiser le jeu si c'est une nouvelle partie (pas une reprise)
    if (!initialGameState) {
      setGameState(initializeTetrisGame());
      setCurrentTimerSeconds(0);
    }
    resumeGameLoop();
  }, [initialGameState, onPauseChange]);

  const pauseGame = useCallback(() => {
    setIsPaused(true);
    onPauseChange(true);
    if (timerRef.current) clearInterval(timerRef.current);
    if (gameIntervalRef.current) clearInterval(gameIntervalRef.current);
    saveGameState(gameState.id, userId, { // Supposons que gameState.id existe
      score: gameState.score,
      level: gameState.level,
      currentTimerSeconds: currentTimerSeconds,
      specificGameState: gameLogicRef.current, // Sauvegarder l'état complet
      lastPlayed: new Date(),
    });
  }, [gameState, currentTimerSeconds, onPauseChange, userId]);

  const resumeGame = useCallback(() => {
    setIsPaused(false);
    onPauseChange(false);
    resumeGameLoop();
  }, [onPauseChange]);

  const restartGame = useCallback(() => {
    clearGameState(gameState.id, userId); // Effacer l'ancienne sauvegarde
    setGameState(initializeTetrisGame());
    setCurrentTimerSeconds(0);
    setIsPaused(false); // S'assurer que le jeu n'est plus en pause
    onPauseChange(false);
    resumeGameLoop(); // Redémarrer directement
  }, [gameState.id, userId, onPauseChange]);

  const quitGame = useCallback(() => {
    // Si le jeu n'est pas terminé, sauvegarder avant de quitter
    if (!gameState.gameOver) {
      saveGameState(gameState.id, userId, {
        score: gameState.score,
        level: gameState.level,
        currentTimerSeconds: currentTimerSeconds,
        specificGameState: gameLogicRef.current,
        lastPlayed: new Date(),
      });
    } else {
      clearGameState(gameState.id, userId); // Effacer si la partie est finie
    }
    onGameQuit(); // Revenir à la liste des jeux
  }, [gameState.gameOver, gameState.id, gameState.score, gameState.level, currentTimerSeconds, userId, onGameQuit]);

  const resumeGameLoop = useCallback(() => {
    // Nettoyer les anciens intervalles
    if (timerRef.current) clearInterval(timerRef.current);
    if (gameIntervalRef.current) clearInterval(gameIntervalRef.current);

    // Démarrer le timer
    timerRef.current = setInterval(() => {
      setCurrentTimerSeconds(prev => prev + 1);
    }, 1000);

    // Démarrer la logique de jeu par intervalle (ex: chute des pièces)
    gameIntervalRef.current = setInterval(() => {
      setGameState(prev => {
        const newState = updateGame(prev, 1); // 1 pour 1 tick de temps
        if (newState.gameOver) {
          if (gameIntervalRef.current) clearInterval(gameIntervalRef.current);
          if (timerRef.current) clearInterval(timerRef.current);
          onGameEnd(newState.score, newState.level, currentTimerSeconds); // Notifier la fin du jeu
          clearGameState(gameState.id, userId); // Nettoyer la sauvegarde
        }
        return newState;
      });
    }, 1000 / gameState.level); // Vitesse du jeu augmente avec le niveau
  }, [onGameEnd, currentTimerSeconds, gameState.id, gameState.level, userId]);


  // --- Effets de bord ---

  // Nettoyage des intervalles au démontage
  useEffect(() => {
    return () => {
      if (timerRef.current) clearInterval(timerRef.current);
      if (gameIntervalRef.current) clearInterval(gameIntervalRef.current);
    };
  }, []);

  // Gérer l'état de pause / fin de jeu
  useEffect(() => {
    if (gameState.gameOver) {
      pauseGame(); // Mettre en pause une fois le jeu terminé
      // La modale de fin de jeu sera affichée par GamesPage via onGameEnd
    } else if (isPaused) {
      // S'assurer que le jeu reste en pause
    } else if (!isPaused && !showRulesModal) {
      // Si pas en pause et règles passées, reprendre la boucle de jeu
      // Cette partie est normalement gérée par startGame/resumeGame
    }
  }, [gameState.gameOver, isPaused, showRulesModal, pauseGame]);


  // --- Gestion des entrées clavier (exemple pour Tetris) ---
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (isPaused || gameState.gameOver) return;

      setGameState(prev => {
        let newState = prev;
        switch (event.key) {
          case 'ArrowLeft':
            newState = movePiece(prev, 'left');
            break;
          case 'ArrowRight':
            newState = movePiece(prev, 'right');
            break;
          case 'ArrowDown':
            newState = movePiece(prev, 'down');
            break;
          case 'ArrowUp':
            newState = rotatePiece(prev);
            break;
          case 'p': // Pause avec 'p'
            pauseGame();
            break;
          default:
            break;
        }
        return newState;
      });
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [isPaused, gameState.gameOver, pauseGame]); // Ajout de pauseGame aux dépendances

  // --- Rendu du jeu ---
  return (
    <GameContainer>
      {/* Bouton Pause */}
      {!gameState.gameOver && !showRulesModal && (
        <PauseButton onClick={pauseGame}>
          <FiMenu />
        </PauseButton>
      )}

      <UIHeader>
        <ScoreDisplay score={gameState.score} />
        <GameTimer timeInSeconds={currentTimerSeconds} />
      </UIHeader>

      <GameBoard>
        {gameState.grid.map((row, rIdx) =>
          row.map((cell, cIdx) => (
            <Cell key={`${rIdx}-${cIdx}`} $isFilled={cell !== 0} />
          ))
        )}
        {/* Rendre la pièce en cours de chute par-dessus la grille */}
        {/* ... logique de rendu de currentPiece si pas déjà dans la grille ... */}
      </GameBoard>

      <Controls>
        <button onClick={() => setGameState(prev => movePiece(prev, 'left'))}>Gauche</button>
        <button onClick={() => setGameState(prev => movePiece(prev, 'right'))}>Droite</button>
        <button onClick={() => setGameState(prev => movePiece(prev, 'down'))}>Bas</button>
        <button onClick={() => setGameState(prev => rotatePiece(prev))}>Rotation</button>
      </Controls>

      {/* Modale de règles / fin de jeu / pause */}
      <GameModal
        isOpen={showRulesModal || isPaused || gameState.gameOver}
        title={showRulesModal ? t('game.modal.rulesTitle', 'Règles du Jeu') : 
               isPaused ? t('game.modal.pausedTitle', 'Jeu en Pause') : 
               t('game.modal.gameOverTitle', 'Partie Terminée !')}
        showStartButton={showRulesModal && !initialGameState} // Montrer Start si nouvelles règles et pas de sauvegarde
        showResumeButton={isPaused && !showRulesModal} // Montrer Resume si en pause et pas en règles
        showReturnButton={true} // Toujours montrer Retour
        showRestartButton={isPaused || gameState.gameOver} // Montrer Redémarrer en pause ou fin de jeu
        onStart={startGame}
        onResume={resumeGame}
        onReturn={quitGame}
        onRestart={restartGame}
      >
        {showRulesModal && (
          <p>{t('game.zenTetris.rules', 'Empilez les blocs pour former des lignes complètes et marquez des points. La vitesse augmente avec les niveaux !')}</p>
        )}
        {isPaused && !showRulesModal && (
          <p>{t('game.modal.pausedMessage', 'Votre partie est en pause. Reprenez quand vous êtes prêt.')}</p>
        )}
        {gameState.gameOver && (
          <div>
            <p>{t('game.modal.gameOverMessage', 'Bien joué ! Votre score final est de {{score}} et vous avez atteint le niveau {{level}}.', { score: gameState.score, level: gameState.level })}</p>
            {/* Afficher ici un bouton pour voir le leaderboard ou recommencer */}
          </div>
        )}
      </GameModal>
    </GameContainer>
  );
};

export default GameComponent;
```

### Étape 5.4 : Styles Spécifiques (`styles.ts`)

Gardez les styles spécifiques au jeu dans ce fichier.

```typescript
// src/games/zen-tetris/styles.ts

// ... vos styled components spécifiques au Tetris
```

### Étape 5.5 : Assets Spécifiques (`assets/`)

Placez ici toutes les images, sons, etc., qui sont propres à ce jeu.

## 6. Intégration dans l'Application

Une fois le jeu développé, il doit être intégré dans la page `GamesPage.tsx` et avoir sa propre route.

### Étape 6.1 : Ajouter une Route dans `src/App.tsx`

La page `GamesPage.tsx` servira de point d'entrée pour l'affichage de la liste des jeux et le lancement des jeux individuels. La structure de route pourrait être :

```typescript
// src/App.tsx

import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
// ... autres imports ...

import GamesPage from './pages/GamesPage';
// ... imports d'autres pages ...

const App: React.FC = () => {
  // ...
  return (
    <Router>
      <div className={`app-container ${darkMode ? 'dark-theme' : 'light-theme'}`}>
        {/* ... header, menu ... */}

        <main className="app-main">
          <Routes>
            {/* ... vos routes existantes ... */}
            <Route path="/games" element={<GamesPage />} />
            {/* Plus de routes individuelles ici, GamesPage gérera l'affichage du jeu */}
          </Routes>
        </main>
        
        {/* ... footer ... */}
      </div>
    </Router>
  );
};

export default App;
```
**Note :** Le `GameComponent` de chaque jeu ne sera pas directement une route dans `App.tsx`. Il sera rendu conditionnellement par `GamesPage.tsx`.

### Étape 6.2 : Mettre à Jour `src/pages/GamesPage.tsx`

Cette page listera les jeux, gérera la sélection, l'affichage des détails (bulle d'info), le lancement et la réception des résultats.

```tsx
// src/pages/GamesPage.tsx

import React, { useState, useEffect, useCallback, useContext } from 'react';
import styled, { ThemeContext, DefaultTheme } from 'styled-components';
import { useTranslation } from 'react-i18next';
import { FiPlay, FiInfo, FiTrophy, FiStar } from 'react-icons/fi';
import { GameInfo, GameProps, SavedGameState } from '../games/common/models'; // Modèles de jeu
import { loadGameState, clearGameState, submitScoreToLeaderboard, getPersonalBestScore } from '../games/common/gameUtils'; // Utilitaires
import ReusableModal from '../components/ReusableModal'; // Modale générique
import { useAuth } from '../AuthContext'; // Si vous avez un contexte d'authentification pour userId

// --- Importez vos jeux ici ---
import ZenTetrisGame from '../games/zen-tetris/GameComponent'; // Exemple: Zen Tetris
// import MemoryChallengeGame from '../games/memory-challenge/GameComponent'; // Autre jeu

// --- Styled Components spécifiques à GamesPage ---
const PageContainer = styled.div`
  padding: 1.5rem;
  max-width: 1000px;
  margin: 0 auto;
  color: ${({ theme }) => theme.text};
`;

const PageTitle = styled.h1`
  font-size: 2.2rem;
  color: ${({ theme }) => theme.primary};
  text-align: center;
  margin-bottom: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
`;

const GameGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
`;

const GameCard = styled.div`
  background: ${({ theme }) => theme.surface};
  border-radius: 12px;
  box-shadow: ${({ theme }) => theme.cardShadow};
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: relative;
  transition: transform 0.2s, box-shadow 0.2s;
  &:hover {
    transform: translateY(-5px);
    box-shadow: ${({ theme }) => theme.hoverShadow};
  }
`;

const GameThumbnail = styled.div<{ $imageUrl: string }>`
  width: 100%;
  height: 180px;
  background-image: url(${({ $imageUrl }) => $imageUrl});
  background-size: cover;
  background-position: center;
  position: relative;
`;

const CardContent = styled.div`
  padding: 1rem 1.2rem;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
`;

const CardTitle = styled.h3`
  font-size: 1.4rem;
  color: ${({ theme }) => theme.text};
  margin: 0 0 0.5rem 0;
`;

const CardDescription = styled.p`
  font-size: 0.9rem;
  color: ${({ theme }) => theme.textSecondary};
  line-height: 1.4;
  flex-grow: 1;
`;

const CardFooter = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 1rem;
`;

const PlayButton = styled.button`
  background: ${({ theme }) => theme.primary};
  color: ${({ theme }) => theme.textLight};
  border: none;
  border-radius: 8px;
  padding: 0.6rem 1.2rem;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.2s;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  &:hover { opacity: 0.9; }
`;

const InfoButton = styled.button`
  background: none;
  border: none;
  color: ${({ theme }) => theme.primary};
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  transition: background-color 0.2s;
  &:hover { background-color: ${({ theme }) => theme.surfaceAlt}; }
`;

const GameStats = styled.div`
  display: flex;
  flex-direction: column;
  font-size: 0.85rem;
  color: ${({ theme }) => theme.textMuted};
  gap: 0.2rem;
  span {
    display: flex;
    align-items: center;
    gap: 0.3rem;
  }
`;

// --- Liste des jeux disponibles ---
const GAMES_LIST: GameInfo[] = [
  {
    id: "zen-tetris",
    titleKey: "games.zenTetris.title",
    descriptionKey: "games.zenTetris.description",
    thumbnailUrl: "/assets/images/games/zen_tetris_thumbnail.jpg", // Créez cette image
    component: ZenTetrisGame,
    maxLevels: 100,
    estimatedDurationMinutes: 10,
    tags: ["concentration", "mémoire", "détente"]
  },
  // {
  //   id: "memory-challenge",
  //   titleKey: "games.memoryChallenge.title",
  //   descriptionKey: "games.memoryChallenge.description",
  //   thumbnailUrl: "/assets/images/games/memory_challenge_thumbnail.jpg",
  //   component: MemoryChallengeGame,
  //   maxLevels: 100,
  //   estimatedDurationMinutes: 5,
  //   tags: ["mémoire", "logique"]
  // },
  // ... ajoutez d'autres jeux ici
];

const GamesPage: React.FC = () => {
  const { t } = useTranslation();
  const theme = useContext(ThemeContext) as DefaultTheme;
  const { userId } = useAuth(); // Obtenez l'ID utilisateur via votre contexte d'authentification

  const [activeGameInfo, setActiveGameInfo] = useState<GameInfo | null>(null);
  const [initialGameLoadState, setInitialGameLoadState] = useState<SavedGameState | null>(null);

  const [showInfoModal, setShowInfoModal] = useState(false);
  const [selectedGameForInfo, setSelectedGameForInfo] = useState<GameInfo | null>(null);
  const [currentPersonalBest, setCurrentPersonalBest] = useState<number>(0);

  const [showGameOverModal, setShowGameOverModal] = useState(false);
  const [lastGameResults, setLastGameResults] = useState<{ score: number; level: number; time: number } | null>(null);

  // Charger la meilleure progression personnelle quand la modale info s'ouvre
  useEffect(() => {
    if (showInfoModal && selectedGameForInfo && userId) {
      setCurrentPersonalBest(getPersonalBestScore(selectedGameForInfo.id, userId));
    }
  }, [showInfoModal, selectedGameForInfo, userId]);


  const handlePlayGame = useCallback((game: GameInfo) => {
    // Vérifier si une partie est sauvegardée
    const savedState = userId ? loadGameState(game.id, userId) : null;
    if (savedState) {
      setInitialGameLoadState(savedState);
    } else {
      setInitialGameLoadState(null); // Pas de sauvegarde, commencer frais
    }
    setActiveGameInfo(game); // Lance le jeu en affichant son composant
  }, [userId]);

  const handleGameEnd = useCallback((score: number, levelReached: number, finalTimeSeconds: number) => {
    // Le jeu est terminé, soumettre le score
    if (userId && activeGameInfo) {
      submitScoreToLeaderboard({
        userId,
        gameId: activeGameInfo.id,
        score,
        levelReached,
        // ... autres données pertinentes
      });
      setLastGameResults({ score, level: levelReached, time: finalTimeSeconds });
      setShowGameOverModal(true);
    }
    setActiveGameInfo(null); // Revenir à la liste des jeux
  }, [userId, activeGameInfo]);

  const handleGameQuit = useCallback(() => {
    setActiveGameInfo(null); // Revenir à la liste des jeux
    setInitialGameLoadState(null); // Effacer l'état initial chargé pour la prochaine fois
  }, []);

  const handleOpenInfoModal = (game: GameInfo) => {
    setSelectedGameForInfo(game);
    setShowInfoModal(true);
  };

  const handleCloseInfoModal = () => {
    setShowInfoModal(false);
    setSelectedGameForInfo(null);
  };

  const handleCloseGameOverModal = () => {
      setShowGameOverModal(false);
      setLastGameResults(null);
  }

  // Rendu conditionnel du jeu ou de la liste des jeux
  if (activeGameInfo) {
    const GameComponent = activeGameInfo.component;
    return (
      <GameComponent
        userId={userId} 
        onGameEnd={handleGameEnd}
        onGameQuit={handleGameQuit}
        onPauseChange={() => { /* Vous pouvez mettre à jour un état global ici si besoin */ }}
        initialGameState={initialGameLoadState}
      />
    );
  }

  return (
    <PageContainer>
      <PageTitle>{t('games.title', 'Mini-Jeux de Développement Personnel')}</PageTitle>
      <p style={{textAlign: 'center', color: theme.textMuted}}>{t('games.intro', 'Testez et améliorez vos compétences avec nos mini-jeux amusants et stimulants.')}</p>

      <GameGrid>
        {GAMES_LIST.map((game) => {
          const personalBest = userId ? getPersonalBestScore(game.id, userId) : 0;
          const savedGame = userId ? loadGameState(game.id, userId) : null;
          
          return (
            <GameCard key={game.id}>
              <GameThumbnail $imageUrl={game.thumbnailUrl} />
              <CardContent>
                <CardTitle>{t(game.titleKey)}</CardTitle>
                <CardDescription>{t(game.descriptionKey)}</CardDescription>
                <GameStats>
                    {game.estimatedDurationMinutes && (
                        <span><FiClock /> {t('games.estimatedDuration', 'Durée estimée')}: {game.estimatedDurationMinutes} {t('units.minutes', 'min')}</span>
                    )}
                    {personalBest > 0 && (
                        <span><FiTrophy /> {t('games.personalBest', 'Record personnel')}: {personalBest} {t('units.points', 'pts')}</span>
                    )}
                    {savedGame && (
                        <span><FiStar style={{color: theme.accent}} /> {t('games.savedGameProgress', 'Partie sauvegardée')}: {savedGame.level}</span>
                    )}
                </GameStats>
              </CardContent>
              <CardFooter>
                <PlayButton onClick={() => handlePlayGame(game)}>
                  <FiPlay /> {savedGame ? t('game.resume', 'Reprendre') : t('game.start', 'Commencer')}
                </PlayButton>
                <InfoButton onClick={() => handleOpenInfoModal(game)}>
                  <FiInfo />
                </InfoButton>
              </CardFooter>
            </GameCard>
          );
        })}
      </GameGrid>

      {/* Modale d'informations sur le jeu */}
      {selectedGameForInfo && (
        <ReusableModal
          isOpen={showInfoModal}
          onClose={handleCloseInfoModal}
          title={t(selectedGameForInfo.titleKey)}
          titleIcon={<FiInfo />}
          footerContent={
            <PlayButton onClick={() => { handlePlayGame(selectedGameForInfo); handleCloseInfoModal(); }}>
                <FiPlay /> {loadGameState(selectedGameForInfo.id, userId || "") ? t('game.resume', 'Reprendre') : t('game.start', 'Commencer')}
            </PlayButton>
          }
        >
          <p>{t(selectedGameForInfo.descriptionKey)}</p>
          <p>{t('games.maxLevels', 'Ce jeu contient {{maxLevels}} niveaux de difficulté.', { maxLevels: selectedGameForInfo.maxLevels })}</p>
          {userId && (
              <p>{t('games.yourBestScore', 'Votre meilleur score sur ce jeu est de {{score}} points.', { score: currentPersonalBest })}</p>
          )}
          {selectedGameForInfo.tags && selectedGameForInfo.tags.length > 0 && (
              <p>Mots-clés : {selectedGameForInfo.tags.join(', ')}</p>
          )}
        </ReusableModal>
      )}

      {/* Modale de fin de partie */}
      <ReusableModal
        isOpen={showGameOverModal}
        onClose={handleCloseGameOverModal}
        title={t('game.modal.gameOverTitle', 'Partie Terminée !')}
        titleIcon={<FiTrophy />}
        footerContent={
            <PlayButton onClick={handleCloseGameOverModal}>{t('actions.ok', 'OK')}</PlayButton>
        }
      >
        {lastGameResults && (
            <p>
                {t('game.gameOverSummary', 'Félicitations ! Votre score final est de {{score}} points et vous avez atteint le niveau {{level}} en {{time}} secondes.', {
                    score: lastGameResults.score,
                    level: lastGameResults.level,
                    time: lastGameResults.time
                })}
            </p>
        )}
      </ReusableModal>

    </PageContainer>
  );
};

export default GamesPage;
```

---

Ce guide vous offre une base solide. N'oubliez pas les points suivants :

*   **Internationalisation (i18n) :** Pensez à ajouter les clés de traduction (`games.zenTetris.title`, `games.zenTetris.description`, `game.modal.rulesTitle`, etc.) dans vos fichiers de traduction pour chaque langue supportée.
*   **`userId` :** L'intégration de l'authentification (`useAuth`) est cruciale pour une persistance réelle et des classements multi-utilisateurs. Pour le moment.
*   **`ReusableModal` :** Assurez-vous que le composant `ReusableModal` est bien défini et fonctionnel.
*   **Thèmes :** Les styles utilisent `theme.textLight`, `theme.primary`, `theme.surface`, `theme.cardShadow`, etc. Assurez-vous que ces propriétés sont définies dans votre `DefaultTheme`.

Commencez par implémenter `models.ts`, `gameUtils.ts`, et les composants génériques comme `GameModal`, `GameTimer`, `ScoreDisplay`. Ensuite, créez un premier jeu simple pour valider l'architecture d'intégration sur `GamesPage`.