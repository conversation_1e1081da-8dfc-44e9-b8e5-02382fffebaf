import React, { useState } from 'react';
import styled from 'styled-components';
import { useTranslation } from 'react-i18next';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>yeOff, <PERSON>Loader } from 'react-icons/fi';
import { FcGoogle } from 'react-icons/fc';
import { useAuth } from '../contexts/AuthContext';
import ReusableModal from './ReusableModal';

interface AuthModalProps {
  isOpen: boolean;
  onClose: () => void;
  initialMode?: 'login' | 'signup';
}

const AuthContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  width: 100%;
  max-width: 400px;
  margin: 0 auto;

  @media (max-width: 480px) {
    gap: 1rem;
  }
`;

const TabContainer = styled.div`
  display: flex;
  border-radius: 8px;
  background: ${({ theme }) => theme.surfaceAlt || theme.surface};
  padding: 4px;
  margin-bottom: 1rem;
`;

const Tab = styled.button<{ $active: boolean }>`
  flex: 1;
  padding: 0.75rem 1rem;
  border: none;
  border-radius: 6px;
  background: ${({ $active, theme }) => $active ? theme.primary : 'transparent'};
  color: ${({ $active, theme }) => $active ? '#fff' : theme.text};
  font-weight: ${({ $active }) => $active ? '600' : '400'};
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: ${({ $active, theme }) => $active ? theme.primary : theme.surface};
  }
`;

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

const InputGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`;

const Label = styled.label`
  font-size: 0.875rem;
  font-weight: 500;
  color: ${({ theme }) => theme.text};
`;

const InputContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 0.75rem;
`;

const InputWrapper = styled.div`
  position: relative;
  flex: 1;
`;

const Input = styled.input<{ $hasRightIcon?: boolean }>`
  width: 100%;
  padding: 0.75rem 1rem;
  padding-right: ${({ $hasRightIcon }) => $hasRightIcon ? '2.5rem' : '1rem'};
  border: 2px solid ${({ theme }) => theme.border || '#e1e5e9'};
  border-radius: 8px;
  background: ${({ theme }) => theme.surface};
  color: ${({ theme }) => theme.text};
  font-size: 1rem;
  transition: border-color 0.2s ease;
  box-sizing: border-box;

  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.primary};
    box-shadow: 0 0 0 3px ${({ theme }) => theme.primary}20;
  }

  &::placeholder {
    color: ${({ theme }) => theme.textSecondary || '#6b7280'};
  }

  @media (max-width: 480px) {
    padding: 0.625rem 0.875rem;
    padding-right: ${({ $hasRightIcon }) => $hasRightIcon ? '2.25rem' : '0.875rem'};
    font-size: 0.9rem;
  }
`;

const InputIcon = styled.div`
  color: ${({ theme }) => theme.primary};
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.25rem;
  height: 1.25rem;
  flex-shrink: 0;

  svg {
    width: 1rem;
    height: 1rem;
  }

  @media (max-width: 480px) {
    width: 1rem;
    height: 1rem;

    svg {
      width: 0.875rem;
      height: 0.875rem;
    }
  }
`;

const PasswordToggle = styled.button`
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: ${({ theme }) => theme.textSecondary || '#6b7280'};
  cursor: pointer;
  padding: 0.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.25rem;
  height: 1.25rem;
  border-radius: 4px;
  transition: all 0.2s ease;

  svg {
    width: 1rem;
    height: 1rem;
  }

  &:hover {
    color: ${({ theme }) => theme.text};
    background: ${({ theme }) => theme.border || '#e1e5e9'};
  }

  @media (max-width: 480px) {
    right: 0.625rem;
    width: 1rem;
    height: 1rem;
    padding: 0.125rem;

    svg {
      width: 0.875rem;
      height: 0.875rem;
    }
  }
`;

const SubmitButton = styled.button<{ $loading?: boolean }>`
  width: 100%;
  padding: 0.875rem 1rem;
  background: ${({ theme }) => theme.primary};
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: ${({ $loading }) => $loading ? 'not-allowed' : 'pointer'};
  opacity: ${({ $loading }) => $loading ? 0.7 : 1};
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;

  &:hover:not(:disabled) {
    background: ${({ theme }) => theme.primaryDark || theme.primary};
    transform: translateY(-1px);
  }

  &:disabled {
    cursor: not-allowed;
    opacity: 0.6;
  }
`;

const Divider = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;
  margin: 1rem 0;

  &::before,
  &::after {
    content: '';
    flex: 1;
    height: 1px;
    background: ${({ theme }) => theme.border || '#e1e5e9'};
  }

  span {
    color: ${({ theme }) => theme.textSecondary || '#6b7280'};
    font-size: 0.875rem;
  }
`;

const GoogleButton = styled.button`
  width: 100%;
  padding: 0.875rem 1rem;
  background: white;
  color: #374151;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;

  &:hover {
    background: #f9fafb;
    border-color: #d1d5db;
    transform: translateY(-1px);
  }
`;

const GuestButton = styled.button`
  width: 100%;
  padding: 0.75rem 1rem;
  background: transparent;
  color: ${({ theme }) => theme.textSecondary || '#6b7280'};
  border: 1px solid ${({ theme }) => theme.border || '#e1e5e9'};
  border-radius: 8px;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: ${({ theme }) => theme.surface};
    color: ${({ theme }) => theme.text};
  }
`;

const ErrorMessage = styled.div`
  color: #ef4444;
  font-size: 0.875rem;
  text-align: center;
  padding: 0.5rem;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 6px;
`;

const TrialInfo = styled.div`
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1rem;
  border-radius: 8px;
  text-align: center;
  margin-bottom: 1rem;

  h4 {
    margin: 0 0 0.5rem 0;
    font-size: 1rem;
    font-weight: 600;
  }

  p {
    margin: 0;
    font-size: 0.875rem;
    opacity: 0.9;
    line-height: 1.4;
  }

  @media (max-width: 480px) {
    padding: 0.75rem;

    h4 {
      font-size: 0.9rem;
    }

    p {
      font-size: 0.8rem;
    }
  }
`;

const AuthModal: React.FC<AuthModalProps> = ({ isOpen, onClose, initialMode = 'login' }) => {
  const { t } = useTranslation();
  const { signIn, signUp, signInWithGoogle, signInAsGuest } = useAuth();

  const [mode, setMode] = useState<'login' | 'signup'>(initialMode);
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    confirmPassword: '',
    displayName: ''
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    if (error) setError('');
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setLoading(true);

    try {
      if (mode === 'signup') {
        if (formData.password !== formData.confirmPassword) {
          throw new Error('Les mots de passe ne correspondent pas');
        }
        if (formData.password.length < 6) {
          throw new Error('Le mot de passe doit contenir au moins 6 caractères');
        }
        await signUp(formData.email, formData.password, formData.displayName);
      } else {
        await signIn(formData.email, formData.password);
      }
      onClose();
    } catch (error: any) {
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleGoogleSignIn = async () => {
    setError('');
    setLoading(true);
    try {
      await signInWithGoogle();
      onClose();
    } catch (error: any) {
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleGuestSignIn = async () => {
    setError('');
    setLoading(true);
    try {
      await signInAsGuest();
      onClose();
    } catch (error: any) {
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setFormData({
      email: '',
      password: '',
      confirmPassword: '',
      displayName: ''
    });
    setError('');
    setShowPassword(false);
    setShowConfirmPassword(false);
  };

  const switchMode = (newMode: 'login' | 'signup') => {
    setMode(newMode);
    resetForm();
  };

  return (
    <ReusableModal
      isOpen={isOpen}
      onClose={onClose}
      title={mode === 'login' ? t('auth.signIn', 'Se connecter') : t('auth.signUp', 'Créer un compte')}
      maxWidth="450px"
      isLoading={loading}
      showCloseButton={false}
      closeOnOverlayClick={false}
    >
      <AuthContainer>
        {mode === 'signup' && (
          <TrialInfo>
            <h4>🎉 {t('auth.trialOffer', 'Essai Premium Gratuit')}</h4>
            <p>{t('auth.trialDescription', 'Profitez de 14 jours d\'accès premium gratuit lors de votre inscription !')}</p>
          </TrialInfo>
        )}

        <TabContainer>
          <Tab $active={mode === 'login'} onClick={() => switchMode('login')}>
            {t('auth.signIn', 'Se connecter')}
          </Tab>
          <Tab $active={mode === 'signup'} onClick={() => switchMode('signup')}>
            {t('auth.signUp', 'S\'inscrire')}
          </Tab>
        </TabContainer>

        {error && <ErrorMessage>{error}</ErrorMessage>}

        <Form onSubmit={handleSubmit}>
          {mode === 'signup' && (
            <InputGroup>
              <Label>{t('auth.displayName', 'Nom d\'affichage')}</Label>
              <InputContainer>
                <InputIcon><FiUser /></InputIcon>
                <InputWrapper>
                  <Input
                    type="text"
                    name="displayName"
                    value={formData.displayName}
                    onChange={handleInputChange}
                    placeholder={t('auth.displayNamePlaceholder', 'Votre nom')}
                  />
                </InputWrapper>
              </InputContainer>
            </InputGroup>
          )}

          <InputGroup>
            <Label>{t('auth.email', 'Email')}</Label>
            <InputContainer>
              <InputIcon><FiMail /></InputIcon>
              <InputWrapper>
                <Input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  placeholder={t('auth.emailPlaceholder', '<EMAIL>')}
                  required
                />
              </InputWrapper>
            </InputContainer>
          </InputGroup>

          <InputGroup>
            <Label>{t('auth.password', 'Mot de passe')}</Label>
            <InputContainer>
              <InputIcon><FiLock /></InputIcon>
              <InputWrapper>
                <Input
                  type={showPassword ? 'text' : 'password'}
                  name="password"
                  value={formData.password}
                  onChange={handleInputChange}
                  placeholder={t('auth.passwordPlaceholder', 'Votre mot de passe')}
                  $hasRightIcon={true}
                  required
                />
                <PasswordToggle
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? <FiEyeOff /> : <FiEye />}
                </PasswordToggle>
              </InputWrapper>
            </InputContainer>
          </InputGroup>

          {mode === 'signup' && (
            <InputGroup>
              <Label>{t('auth.confirmPassword', 'Confirmer le mot de passe')}</Label>
              <InputContainer>
                <InputIcon><FiLock /></InputIcon>
                <InputWrapper>
                  <Input
                    type={showConfirmPassword ? 'text' : 'password'}
                    name="confirmPassword"
                    value={formData.confirmPassword}
                    onChange={handleInputChange}
                    placeholder={t('auth.confirmPasswordPlaceholder', 'Confirmez votre mot de passe')}
                    $hasRightIcon={true}
                    required
                  />
                  <PasswordToggle
                    type="button"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  >
                    {showConfirmPassword ? <FiEyeOff /> : <FiEye />}
                  </PasswordToggle>
                </InputWrapper>
              </InputContainer>
            </InputGroup>
          )}

          <SubmitButton type="submit" disabled={loading} $loading={loading}>
            {loading && <FiLoader style={{ animation: 'spin 1s linear infinite' }} />}
            {mode === 'login'
              ? t('auth.signInButton', 'Se connecter')
              : t('auth.signUpButton', 'Créer mon compte')
            }
          </SubmitButton>
        </Form>

        <Divider>
          <span>{t('auth.or', 'ou')}</span>
        </Divider>

        <GoogleButton onClick={handleGoogleSignIn} disabled={loading}>
          <FcGoogle size={20} />
          {t('auth.continueWithGoogle', 'Continuer avec Google')}
        </GoogleButton>

        <GuestButton onClick={handleGuestSignIn} disabled={loading}>
          {t('auth.continueAsGuest', 'Continuer en tant qu\'invité')}
        </GuestButton>
      </AuthContainer>
    </ReusableModal>
  );
};

export default AuthModal;
