#!/bin/bash

echo "========================================"
echo "   PiKnowKyo Development Environment"
echo "========================================"
echo ""
echo "Starting both applications..."
echo ""

echo "[1/2] Starting PiKnowKyo App (port 3000)..."
cd piknowkyo
npm run dev &
PIKNOWKYO_PID=$!
cd ..

echo "[2/2] Starting Pmanager Admin (port 3001)..."
cd Pmanager
npm run dev &
PMANAGER_PID=$!
cd ..

echo ""
echo "========================================"
echo "   Applications started successfully!"
echo "========================================"
echo ""
echo "PiKnowKyo App:     http://localhost:3000"
echo "Pmanager Admin:    http://localhost:3001"
echo ""
echo "Press Ctrl+C to stop both applications..."

# Function to cleanup on exit
cleanup() {
    echo ""
    echo "Stopping applications..."
    kill $PIKNOWKYO_PID 2>/dev/null
    kill $PMANAGER_PID 2>/dev/null
    echo "Applications stopped."
    exit 0
}

# Trap Ctrl+C
trap cleanup INT

# Wait for user to stop
wait
